import { randomUUID } from 'node:crypto';
import { getFirstName } from '@bybeam/formatting';
import { NotificationType } from '@bybeam/notification-client';
import {
  ApplicantTypeEntity,
  CaseEntity,
  CaseParticipantEntity,
  InvitationCodeEntity,
} from '@bybeam/platform-entities';
import sort from '@bybeam/platform-lib/utilities/sort';
import {
  ApplicantType,
  ApplicantTypeRole,
  Case,
  CaseParticipant,
  InvitationCode,
  Participant,
  Program,
} from '@bybeam/platform-types';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import { In, Repository } from 'typeorm';
import { ApplicationUpdateInput, CreatedParticipant } from '../@types/participants.js';
import { NotificationService } from '../notification/notification.service.js';
import { generateCode } from '../utils/codes.js';
import { filterExistingParticipants, getNewParticipants } from './utils.js';

@Injectable()
export class InvitationService {
  constructor(
    @InjectPinoLogger(InvitationService.name) private readonly logger: PinoLogger,
    @InjectRepository(ApplicantTypeEntity) private applicantTypeRepo: Repository<ApplicantType>,
    @InjectRepository(CaseEntity) private caseRepo: Repository<Case>,
    @InjectRepository(CaseParticipantEntity) private participantRepo: Repository<CaseParticipant>,
    @InjectRepository(InvitationCodeEntity) private invitationRepo: Repository<InvitationCode>,
    private readonly notificationService: NotificationService,
  ) {}

  async handleApplicationUpdate({ application, config }: ApplicationUpdateInput) {
    if (application?.case?.program.applicantTypes.length <= 1)
      return { message: 'Will not invite' };

    const participants = getNewParticipants({ application, config });

    const { newParticipants, removeParticipants } = filterExistingParticipants({
      application,
      participants,
    });

    const message = [];

    if (newParticipants.length) {
      const submitterType = application.submitter.applicantProfile.applicantType;
      await this.inviteParticipants(application.case, newParticipants, submitterType);
      message.push(`Invited ${newParticipants.length} new participants`);
    } else message.push('No new participants found');

    if (removeParticipants.length) {
      await this.removeParticipants(removeParticipants.map(({ id }) => id));
      message.push(`Removed ${removeParticipants.length} existing participants`);
    } else message.push('No existing participants removed');

    return { message: message.join(', ') };
  }

  async inviteCaseParticipant(caseId: string, participant: Participant) {
    const { case: case_ } = await this.validateInvitation(caseId, participant);

    const submitterType = sort(case_.applications ?? [], {
      accessor: (item) => item.createdAt,
    })?.[0]?.submitter?.applicantProfile?.applicantType;

    const caseParticipants = await this.inviteParticipants(case_, [participant], submitterType);

    return { newCaseParticipantId: caseParticipants[0].id };
  }

  private async inviteParticipants(
    { id: caseId, program }: Case,
    participants: Participant[],
    submitterType: ApplicantType,
  ) {
    const caseParticipants = await this.createParticipants(caseId, participants);
    await this.sendInvitationEmail(program, caseParticipants, submitterType);
    return caseParticipants;
  }

  private async createParticipants(
    caseId: string,
    participants: Participant[],
  ): Promise<CreatedParticipant[]> {
    let participantIds: string[] = [];
    try {
      const mappedParticipants = participants.map((participant) => {
        const id = randomUUID() as string;
        return {
          id,
          caseId,
          applicantTypeId: participant.applicantTypeId,
          name: participant.name,
          email: participant.email,
          invitationCodes: [{ caseParticipantId: id, code: generateCode() }],
        };
      });
      participantIds = mappedParticipants.map(({ id }) => id);

      await this.participantRepo.save(mappedParticipants);
      await this.invitationRepo.save(
        mappedParticipants.flatMap(({ invitationCodes }) => invitationCodes),
      );

      return mappedParticipants.map((participant) => ({
        ...participant,
        code: participant.invitationCodes[0].code,
      }));
    } catch (err) {
      if (participantIds?.length) await this.removeParticipants(participantIds);
      throw err;
    }
  }

  private async sendInvitationEmail(
    program: Program,
    caseParticipants: CreatedParticipant[],
    senderType?: ApplicantType,
  ) {
    const participantTypes = await this.applicantTypeRepo.findBy({
      id: In(caseParticipants.map(({ applicantTypeId }) => applicantTypeId)),
    });

    const applicantTypesById = new Map<string, ApplicantType>();
    for (const type of participantTypes) applicantTypesById.set(type.id, type);

    const applicantTypeName = senderType
      ? (program.applicantTypes.find(({ applicantTypeId }) => applicantTypeId === senderType.id)
          ?.nameOverride ?? senderType.name)
      : 'Applicant';

    return this.notificationService.sendEmail({
      recipients: caseParticipants.map(({ name, email, code, applicantTypeId }) => ({
        name:
          applicantTypesById.get(applicantTypeId)?.role === ApplicantTypeRole.ThirdParty
            ? name
            : getFirstName(name),
        email,
        variables: { code },
      })),
      notificationConfig: {
        type: NotificationType.MultipartyInvitation,
        context: {
          partnerId: program.partnerId,
          programId: program.id,
        },
        variables: {
          PROGRAM_NAME: program.name,
          INVITER_TYPE: applicantTypeName.toLowerCase(),
          CODE: '%recipient.code%',
        },
      },
    });
  }

  private async removeParticipants(ids: string[]) {
    if (!ids?.length) return;
    await this.invitationRepo.softDelete({ caseParticipantId: In(ids) });
    await this.participantRepo.softDelete({ id: In(ids) });
  }

  private async validateInvitation(
    caseId: string,
    participant: Participant,
  ): Promise<{ case: Case }> {
    if (!participant.email || !participant.name || !participant.applicantTypeId)
      throw new Error('Participant should have email/name/type defined.');
    const case_ = await this.caseRepo.findOne({
      where: { id: caseId },
      relations: [
        'program',
        'program.applicantTypes',
        'applications',
        'applications.submitter',
        'applications.submitter.applicantProfile',
        'applications.submitter.applicantProfile.applicantType',
      ],
    });
    if (!case_) throw new Error('No case found.');
    if (case_.program?.applicantTypes?.length <= 1)
      throw new Error('Program does not have any third party applicant types.');

    const applicantType = await this.applicantTypeRepo.findOneBy({
      id: participant.applicantTypeId,
    });
    if (!applicantType) throw new Error('No applicant type found.');

    const existingParticipant = await this.participantRepo.findOneBy({
      applicantTypeId: participant.applicantTypeId,
      name: participant.name,
      email: participant.email,
      caseId,
    });
    if (existingParticipant) throw new Error('Invitation has been already sent.');

    return { case: case_ };
  }
}
