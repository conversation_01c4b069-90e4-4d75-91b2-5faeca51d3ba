import {
  ApplicationSubmittedEvent,
  ApplicationVersionCreatedEvent,
  EventHandlerResponse,
} from '@bybeam/common-proto';
import { ApplicationEntity, ApplicationVersionEntity } from '@bybeam/platform-entities';
import { Application, ApplicationConfig, ApplicationVersion } from '@bybeam/platform-types';
import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import { Repository } from 'typeorm';
import { ConfigService } from '../config/config.service.js';
import { InvitationService } from '../invitation/invitation.service.js';
import { LinkingService } from '../linking/linking.service.js';
import { getLinkCode } from '../utils/answers.js';

@Injectable()
export class ApplicationService {
  constructor(
    @InjectPinoLogger(ApplicationService.name) private readonly logger: PinoLogger,
    @Inject(ConfigService) private configService: ConfigService,
    @Inject(LinkingService) private linkingService: LinkingService,
    @Inject(InvitationService) private invitationService: InvitationService,
    @InjectRepository(ApplicationEntity) private applicationRepo: Repository<Application>,
    @InjectRepository(ApplicationVersionEntity)
    private applicationVersionRepo: Repository<ApplicationVersion>,
  ) {}

  private async getApplicationConfig(application: Application) {
    const config = await this.configService.getApplicationConfig({
      programId: [application.case.programId],
    });
    const applicantConfig = config?.configuration?.find(
      ({ applicantTypeId }) =>
        applicantTypeId === application?.submitter?.applicantProfile?.applicantTypeId,
    );

    if (!applicantConfig?.configuration?.config) return undefined;
    return JSON.parse(applicantConfig.configuration.config) as ApplicationConfig;
  }

  private async getApplication(id: string) {
    this.logger.info({ id }, 'getApplication: getting application');
    try {
      return this.applicationRepo.findOne({
        where: { id },
        relations: [
          'latestVersion',
          'latestVersion.applicationAnswers',
          'case',
          'case.participants',
          'case.participants.linkAttempts',
          'case.program',
          'case.program.features',
          'case.program.applicantTypes',
          'submitter.applicantProfile',
          'submitter.applicantProfile.applicantType',
          'linkAttempts',
        ],
        // TODO this does not work, some issue with our entities? many to many rels?
        // link attempts, applicant profile, and latest version all fail
        // relationLoadStrategy: 'query',
      });
    } catch (error) {
      this.logger.error({ error }, 'getApplication: error getting application');
      return undefined;
    }
  }

  private async getApplicationByVersion(id: string) {
    const version = await this.applicationVersionRepo.findOne({ where: { id } });
    if (!version) return undefined;
    return this.getApplication(version.applicationId);
  }

  private async handleApplicationUpdate(getApplication: () => Promise<Application>) {
    this.logger.info('handleApplicationUpdate: handling application update');
    let application = await getApplication();
    if (!application) return { message: 'No application found' };

    const config = await this.getApplicationConfig(application);
    if (!config) return { message: 'No application config found' };

    const message = [];

    const linkCode = getLinkCode(application);
    if (linkCode) {
      const linkResult = await this.linkingService.handleApplicationUpdate(application, linkCode);
      message.push(linkResult.message);
      application = await getApplication();
    }

    message.push(
      (await this.invitationService.handleApplicationUpdate({ application, config })).message,
    );

    return { message: message.join('. ') };
  }

  async handleApplicationSubmitted(
    request: ApplicationSubmittedEvent,
  ): Promise<EventHandlerResponse> {
    this.logger.info({ request }, 'handleApplicationSubmitted: received event');
    return this.handleApplicationUpdate(() => this.getApplication(request.applicationId));
  }

  async handleApplicationVersionCreated(
    request: ApplicationVersionCreatedEvent,
  ): Promise<EventHandlerResponse> {
    this.logger.info({ request }, 'handleApplicationVersionCreated: received event');
    return this.handleApplicationUpdate(() => this.getApplicationByVersion(request.versionId));
  }
}
