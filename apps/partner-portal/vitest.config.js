import path from 'node:path';
/// reference types=”vitest” />
import react from '@vitejs/plugin-react';
import graphqlLoader from 'vite-plugin-graphql-loader';
import tsconfigPaths from 'vite-tsconfig-paths';
import { defineConfig } from 'vitest/config';
import { configDefaults } from 'vitest/config';

if (process.env.NODE_ENV === 'test') process.env.TZ = 'UTC';

export default defineConfig({
  plugins: [tsconfigPaths(), graphqlLoader(), react()],
  resolve: {
    alias: {
      '@/spa-legacy': path.resolve(__dirname, 'src/spa-legacy'),

    },
  },
  test: {
    globals: true,
    environment: 'jsdom',
    environmentOptions: {
      jsdom: {
        url: 'http://localhost',
        resources: 'usable',
        runScripts: 'dangerously',
      },
    },
    clearMocks: true,
    setupFiles: [
      './src/tests/mocks/next-router-mock.ts',
      './src/tests/hooks/global.ts',
      './src/spa-legacy/common/utilities/dayjsConfig.ts',
    ],
    coverage: {
      provider: 'v8',
      include: ['src/**/*.ts?(x)'],
      exclude: [
        ...(configDefaults.coverage.exclude ?? []),
        'src/**/*.stories.tsx',
      ],
      reporter: ['text', 'text-summary', 'cobertura'],
      thresholds: {
        branches: 74,
        functions: 40,
        lines: 50,
        statements: 50,
      },
    },
  },
});
