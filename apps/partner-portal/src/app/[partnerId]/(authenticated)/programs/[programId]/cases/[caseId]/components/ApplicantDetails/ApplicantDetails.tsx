import CopyToClipboard from '@/app/components/ui/CopyToClipboard/CopyToClipboard';
import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import useProgram from '@/spa-legacy/common/hooks/contexts/useProgram';
import { hasMultiParty } from '@/spa-legacy/common/utilities/applicantType';
import { formatIfPhone, formatSnakeToTitleCase } from '@/spa-legacy/common/utilities/format';
import { PORTAL_ROUTES } from '@/spa-legacy/portal/PortalRoutes';
import { makeRoute } from '@/spa-legacy/utilities/Routes';
import { checkPartnerHasFeature } from '@/spa-legacy/utilities/checkFeature';
import { Address, ApplicantTypeRole, FeatureName, User } from '@bybeam/platform-types';
import { EnvelopeClosedIcon } from '@radix-ui/react-icons';
import {
  Bad<PERSON>,
  DataList,
  Flex,
  HoverCard,
  Link as RadixLink,
  Text,
  Tooltip,
} from '@radix-ui/themes';
import Link from 'next/link';
import { usePostHog } from 'posthog-js/react';
import React from 'react';
import styles from './ApplicantDetails.module.css';

// TODO: migrate to graphql-codegen for client-side types
type ApplicantLoginDetails = {
  recentLogin?: {
    timestamp?: string | Date;
    location?: string;
    isVpn?: boolean;
  };
};

interface ApplicantDetailsProps {
  applicant?: User & ApplicantLoginDetails;
}

const MailingAddress = ({ address }: { address?: Address }) => {
  if (!address) {
    return <div>Not provided</div>;
  }

  return (
    <Flex direction="column">
      <div>{address.addressLine1}</div>
      <div>{address.addressLine2}</div>
      <div>
        {address.city}, {address.state} {address.zip}
      </div>
    </Flex>
  );
};

const EmailAddress = ({ email }: { email?: string }) => {
  if (!email) {
    return <div>Not provided</div>;
  }

  return (
    <Flex asChild minWidth="0">
      <RadixLink href={`mailto:${email}`} className={styles.EmailAddressLink}>
        <Tooltip content={email}>
          <Flex minWidth="0" gap="1" align="center">
            <span className={styles.EmailAddressIcon}>
              <EnvelopeClosedIcon />
            </span>
            <Flex minWidth="0">
              <Text truncate>{email}</Text>
            </Flex>
          </Flex>
        </Tooltip>
      </RadixLink>
    </Flex>
  );
};

const PhoneNumber = ({ phone }: { phone?: string }) => {
  if (!phone) {
    return <div>Not provided</div>;
  }

  return <div>{formatIfPhone(phone)}</div>;
};

const ApplicantProfileLink = ({
  applicant,
  children,
}: { applicant: User; children: React.ReactNode }) => {
  return (
    <Tooltip content="Go to applicant profile">
      <RadixLink asChild>
        <Link
          href={{ pathname: makeRoute(PORTAL_ROUTES.APPLICANT_PROFILE, { userId: applicant.id }) }}
        >
          {children}
        </Link>
      </RadixLink>
    </Tooltip>
  );
};

const ApplicantDetails: React.FC<ApplicantDetailsProps> = ({ applicant }) => {
  if (!applicant) {
    return <div>Applicant data is not available</div>;
  }

  const partner = usePartner();
  const program = useProgram();
  const posthog = usePostHog();
  const isMultiParty = program && hasMultiParty(program);
  const applicantType = applicant.applicantProfile?.applicantType;
  const includeRecentLogin =
    checkPartnerHasFeature(partner, FeatureName.UserValidation) &&
    posthog?.isFeatureEnabled?.(POSTHOG_FEATURE_FLAGS.recentLoginDetails);

  const programApplicantType = program?.applicantTypes?.find((type) => {
    return type.applicantType.id === applicantType?.id;
  });

  return (
    <div>
      {applicant && (
        <DataList.Root className={styles.dataList}>
          <DataList.Item>
            <DataList.Label>Name</DataList.Label>
            <DataList.Value>
              <Text truncate>{applicant.name}</Text>
            </DataList.Value>
          </DataList.Item>
          {isMultiParty ? (
            <DataList.Item>
              <DataList.Label>Applicant type</DataList.Label>
              <DataList.Value>
                <Tooltip
                  content={formatSnakeToTitleCase(applicant.applicantProfile?.applicantType?.role)}
                >
                  <Badge
                    color={
                      applicant.applicantProfile?.applicantType?.role ===
                      ApplicantTypeRole.FirstParty
                        ? 'blue'
                        : 'gray'
                    }
                  >
                    {programApplicantType?.nameOverride ||
                      applicantType?.name ||
                      'Unknown participant type'}
                  </Badge>
                </Tooltip>
              </DataList.Value>
            </DataList.Item>
          ) : null}
          <DataList.Item>
            <DataList.Label>Beam User ID</DataList.Label>
            <DataList.Value>
              <CopyToClipboard.Root>
                <CopyToClipboard.Content>
                  <ApplicantProfileLink applicant={applicant}>
                    {applicant.displayId}
                  </ApplicantProfileLink>
                </CopyToClipboard.Content>
                <CopyToClipboard.Trigger text={applicant.displayId} contentType="Applicant_Id" />
              </CopyToClipboard.Root>
            </DataList.Value>
          </DataList.Item>
          <DataList.Item>
            <DataList.Label>Phone</DataList.Label>
            <DataList.Value>
              {applicant.phone ? (
                <CopyToClipboard.Root>
                  <CopyToClipboard.Content>
                    <PhoneNumber phone={applicant.phone} />
                  </CopyToClipboard.Content>
                  <CopyToClipboard.Trigger text={applicant.phone} contentType="Applicant_Phone" />
                </CopyToClipboard.Root>
              ) : (
                'Phone not provided'
              )}
            </DataList.Value>
          </DataList.Item>
          <DataList.Item>
            <DataList.Label>Mailing Address</DataList.Label>
            <DataList.Value>
              <MailingAddress address={applicant.mailingAddress} />
            </DataList.Value>
          </DataList.Item>
          <DataList.Item>
            <DataList.Label>Email</DataList.Label>
            <DataList.Value>
              <EmailAddress email={applicant.email} />
            </DataList.Value>
          </DataList.Item>
          {includeRecentLogin ? (
            <DataList.Item>
              <DataList.Label>Last login</DataList.Label>
              <DataList.Value>
                {applicant.recentLogin?.timestamp ? (
                  <HoverCard.Root>
                    <HoverCard.Trigger>
                      <span>{new Date(applicant.recentLogin.timestamp).toLocaleString()}</span>
                    </HoverCard.Trigger>
                    <HoverCard.Content>
                      <Flex direction="column" gap="1">
                        <Flex direction="column" mb="2">
                          <Text size="2" weight="medium">
                            Login details
                          </Text>
                          <Text size="1" color="gray">
                            Based on the user's IP address at most recent login
                          </Text>
                        </Flex>
                        <Flex direction="column" gap="1" mb="1">
                          <Text size="2">
                            🕦{' '}
                            {new Date(applicant.recentLogin.timestamp).toLocaleString(undefined, {
                              timeZoneName: 'short',
                            })}
                          </Text>
                          {applicant.recentLogin?.location ? (
                            <Text size="2">📍 {applicant.recentLogin.location}</Text>
                          ) : null}
                          {applicant.recentLogin?.isVpn ? (
                            <Text size="2">🔒 VPN detected by provider</Text>
                          ) : null}
                        </Flex>
                      </Flex>
                    </HoverCard.Content>
                  </HoverCard.Root>
                ) : (
                  'Not available'
                )}
              </DataList.Value>
            </DataList.Item>
          ) : null}
        </DataList.Root>
      )}
    </div>
  );
};

export default ApplicantDetails;
