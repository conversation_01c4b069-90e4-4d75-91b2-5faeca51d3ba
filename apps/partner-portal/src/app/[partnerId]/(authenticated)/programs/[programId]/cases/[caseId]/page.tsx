'use client';
import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import useProgram from '@/spa-legacy/common/hooks/contexts/useProgram';
import { checkPartnerHasFeature } from '@/spa-legacy/utilities/checkFeature';
import { useQuery } from '@apollo/client';
import { Case, FeatureName } from '@bybeam/platform-types';
import { useParams } from 'next/navigation';
import { useFeatureFlagEnabled } from 'posthog-js/react';
import { CaseDetails } from './components';
import styles from './page.module.css';
import GetCaseDetail from './pageQuery.graphql';

export default function ProgramsCasePage() {
  const { caseId } = useParams<{ programId: string; caseId: string }>();
  const partner = usePartner();
  const program = useProgram();
  const includeRecentLogin =
    checkPartnerHasFeature(partner, FeatureName.UserValidation) &&
    useFeatureFlagEnabled(POSTHOG_FEATURE_FLAGS.recentLoginDetails);

  const caseQuery = useQuery<
    { cases: { cases: Case[] } },
    { id: string; includeRecentLogin: boolean }
  >(GetCaseDetail, {
    variables: {
      id: caseId,
      includeRecentLogin: includeRecentLogin ?? false,
    },
  });

  const caseDetail = caseQuery?.data?.cases?.cases[0];

  return (
    <>
      {caseQuery?.loading ? (
        'Loading...'
      ) : caseDetail && program ? (
        <div className={styles.caseReviewContent}>
          <div className={styles.caseReview}>
            <CaseDetails program={program} />
          </div>
        </div>
      ) : (
        'Case not found'
      )}
    </>
  );
}
