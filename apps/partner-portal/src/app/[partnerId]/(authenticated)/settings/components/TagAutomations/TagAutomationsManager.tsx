import Typography from '@/spa-legacy/common/components/Typography';
import { useMutation, useQuery } from '@apollo/client';
import {
  DeleteTagAutomationInput,
  MutationResponse,
  TagAutomation,
  TagAutomationActionType,
  TagAutomationTriggerType,
} from '@bybeam/platform-types';
import { AlertDialog, Button, Flex, IconButton, Table } from '@radix-ui/themes';
import GetTagAutomations from './GetTagAutomations.graphql';
import { Pencil2Icon, PlusIcon, TrashIcon } from '@radix-ui/react-icons';
import styles from './TagAutomations.module.css';
import { useState } from 'react';
import TagAutomationDialog from './TagAutomationDialog';
import { TagChips } from '@/spa-legacy/portal/components/CasesTable/cells/TagsCell';
import DeleteTagAutomation from './DeleteTagAutomation.graphql';
import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';

export const getActionType = (type: TagAutomationActionType) => {
  switch (type) {
    case TagAutomationActionType.AddTag:
      return 'Add Tag';
    default:
      return 'Unknown';
  }
};

export const getTriggerType = (type: TagAutomationTriggerType) => {
  switch (type) {
    case TagAutomationTriggerType.ApplicationSubmitted:
      return 'Application Submitted';

    default:
      return 'Unknown';
  }
};

export default function TagAutomationsManager() {
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);
  const [selectedTagAutomation, setSelectedTagAutomation] = useState<TagAutomation>();
  const { data: tagAutomationsData, refetch } = useQuery<
    { tagAutomations: TagAutomation[] },
    { externalId: string }
  >(GetTagAutomations);
  const [deleteTagAutomation, { error: mutationError, loading: submitting, reset: resetMutation }] =
    useMutation<
      { tagAutomation: { delete: MutationResponse<TagAutomation> } },
      { input: DeleteTagAutomationInput }
    >(DeleteTagAutomation);

  const tagAutomations = tagAutomationsData?.tagAutomations ?? [];
  const { showSnackbar } = useSnackbar();

  const handleDeactivate = async (id: string) => {
    try {
      await deleteTagAutomation({
        variables: {
          input: {
            id,
          },
        },
      });
      refetch();
    } catch (e) {
      console.error(e);
      showSnackbar('Failed to delete tag automation');
    }
  };

  return (
    <div className="flex flex-col gap-6">
      <div className={styles.header}>
        <Typography variant="h2">Tag Automations</Typography>
        <Button variant="outline" onClick={() => setIsDialogOpen(true)}>
          <PlusIcon />
          Add Automation
        </Button>
      </div>
      <div>
        <Table.Root variant="surface">
          <Table.Header>
            <Table.Row>
              <Table.ColumnHeaderCell>Trigger</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Action</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Tag(s)</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Programs</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell> </Table.ColumnHeaderCell>
            </Table.Row>
          </Table.Header>

          <Table.Body>
            {tagAutomations?.map((automation) => (
              <Table.Row key={automation.id}>
                <Table.Cell>{getTriggerType(automation.triggerType)}</Table.Cell>
                <Table.RowHeaderCell>{getActionType(automation.actionType)}</Table.RowHeaderCell>
                <Table.Cell className="flex gap-2">
                  <TagChips tags={automation.tags} />
                </Table.Cell>
                <Table.Cell>
                  {automation.program ? automation.program.name : 'All programs'}
                </Table.Cell>
                <Table.Cell>
                  <Flex gap="4" align="center">
                    <AlertDialog.Root>
                      <AlertDialog.Trigger>
                        <IconButton color="red" variant="ghost">
                          <TrashIcon />
                        </IconButton>
                      </AlertDialog.Trigger>
                      <AlertDialog.Content maxWidth="450px">
                        <AlertDialog.Title>Delete Tag Automation</AlertDialog.Title>
                        <AlertDialog.Description size="2">
                          Are you sure? This automation will no longer be accessible and no further
                          automations will trigger.
                        </AlertDialog.Description>

                        <Flex gap="3" mt="4" justify="end">
                          <AlertDialog.Cancel>
                            <Button variant="soft" color="gray">
                              Cancel
                            </Button>
                          </AlertDialog.Cancel>
                          <AlertDialog.Action>
                            <Button
                              variant="solid"
                              color="red"
                              onClick={() => handleDeactivate(automation.id)}
                            >
                              Delete
                            </Button>
                          </AlertDialog.Action>
                        </Flex>
                      </AlertDialog.Content>
                    </AlertDialog.Root>

                    <IconButton
                      variant="ghost"
                      onClick={() => {
                        setSelectedTagAutomation(automation);
                        setIsDialogOpen(true);
                      }}
                    >
                      <Pencil2Icon />
                    </IconButton>
                  </Flex>
                </Table.Cell>
              </Table.Row>
            ))}
            {!tagAutomations.length && (
              <Table.Row>
                <Table.Cell colSpan={5}>No tag automations found</Table.Cell>
              </Table.Row>
            )}
          </Table.Body>
        </Table.Root>
      </div>
      <TagAutomationDialog
        onSuccess={() => {
          setSelectedTagAutomation(undefined);
          setIsDialogOpen(false);
          refetch();
        }}
        setIsDialogOpen={(value) => {
          setIsDialogOpen(value);
          if (!value) {
            setSelectedTagAutomation(undefined);
          }
        }}
        isDialogOpen={isDialogOpen}
        tagAutomation={selectedTagAutomation}
      />
    </div>
  );
}
