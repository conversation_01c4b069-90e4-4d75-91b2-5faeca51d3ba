import Typography from '@/spa-legacy/common/components/Typography';
import { useQuery } from '@apollo/client';
import {
  TagAutomation,
  TagAutomationActionType,
  TagAutomationTriggerType,
} from '@bybeam/platform-types';
import { Badge, Button, IconButton, Table } from '@radix-ui/themes';
import GetTagAutomations from './GetTagAutomations.graphql';
import { Pencil2Icon, PlusIcon } from '@radix-ui/react-icons';
import styles from './TagAutomations.module.css';
import { useState } from 'react';
import TagAutomationDialog from './TagAutomationDialog';
import { TagChips } from '@/spa-legacy/portal/components/CasesTable/cells/TagsCell';

export const getActionType = (type: TagAutomationActionType) => {
  switch (type) {
    case TagAutomationActionType.AddTag:
      return 'Add Tag';
    default:
      return 'Unknown';
  }
};

export const getTriggerType = (type: TagAutomationTriggerType) => {
  switch (type) {
    case TagAutomationTriggerType.ApplicationSubmitted:
      return 'Application Submitted';

    default:
      return 'Unknown';
  }
};

export default function TagAutomationsManager() {
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);
  const [selectedTagAutomation, setSelectedTagAutomation] = useState<TagAutomation>();
  const { data: tagAutomationsData } = useQuery<
    { tagAutomations: TagAutomation[] },
    { externalId: string }
  >(GetTagAutomations);

  const tagAutomations = tagAutomationsData?.tagAutomations ?? [];

  return (
    <div className="flex flex-col gap-6">
      <div className={styles.header}>
        <Typography variant="h2">Tag Automations</Typography>
        <Button variant="outline" onClick={() => setIsDialogOpen(true)}>
          <PlusIcon />
          Add Automation
        </Button>
      </div>
      <div>
        <Table.Root variant="surface">
          <Table.Header>
            <Table.Row>
              <Table.ColumnHeaderCell>Action</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Trigger</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Tag(s)</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Programs</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Status</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell> </Table.ColumnHeaderCell>
            </Table.Row>
          </Table.Header>

          <Table.Body>
            {tagAutomations?.map((automation) => (
              <Table.Row key={automation.id}>
                <Table.RowHeaderCell>{getActionType(automation.actionType)}</Table.RowHeaderCell>
                <Table.Cell>{getTriggerType(automation.triggerType)}</Table.Cell>
                <Table.Cell className="flex gap-2">
                  <TagChips tags={automation.tags} />
                  {/* {automation.tags.map((tag) => (
                    <Badge variant="soft" key={tag.id}>
                      {tag.name}
                    </Badge>
                  ))} */}
                </Table.Cell>
                <Table.Cell>
                  {automation.program ? automation.program.name : 'All programs'}
                </Table.Cell>
                <Table.Cell>
                  {automation.deactivatedAt ? (
                    <Badge color="gray" variant="soft" radius="full">
                      Inactive
                    </Badge>
                  ) : (
                    <Badge color="green" variant="soft" radius="full">
                      Active
                    </Badge>
                  )}
                </Table.Cell>
                <Table.Cell>
                  <IconButton
                    variant="ghost"
                    onClick={() => {
                      setSelectedTagAutomation(automation);
                      setIsDialogOpen(true);
                    }}
                  >
                    <Pencil2Icon />
                  </IconButton>
                </Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
        </Table.Root>
      </div>
      <TagAutomationDialog
        onSuccess={() => {
          setSelectedTagAutomation(undefined);
          setIsDialogOpen(false);
        }}
        setIsDialogOpen={(value) => {
          setIsDialogOpen(value);
          if (!value) {
            setSelectedTagAutomation(undefined);
          }
        }}
        isDialogOpen={isDialogOpen}
        tagAutomation={selectedTagAutomation}
      />
    </div>
  );
}
