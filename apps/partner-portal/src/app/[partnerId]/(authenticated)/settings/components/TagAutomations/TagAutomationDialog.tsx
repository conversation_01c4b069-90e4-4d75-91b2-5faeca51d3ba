import {
  CreateTagAutomationInput,
  CriteriaOperator,
  CriteriaType,
  MutationResponse,
  Partner,
  TagAutomation,
  TagAutomationActionType,
  TagAutomationCriteria,
  TagAutomationTriggerType,
  UpdateTagAutomationInput,
} from '@bybeam/platform-types';
import CreateTagAutomation from './CreateTagAutomation.graphql';
import UpdateTagAutomation from './UpdateTagAutomation.graphql';
import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';
import { useMutation, useQuery } from '@apollo/client';
import GetPartnerTags from './GetPartnerTags.graphql';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import { ErrorMessage } from '@hookform/error-message';
import { Callout, Dialog, Flex, Text, Select, Button, TextField } from '@radix-ui/themes';
import { useForm, Controller, useFieldArray } from 'react-hook-form';
import { getActionType, getTriggerType } from './TagAutomationsManager';
import ReactSelect from 'react-select';
import { useEffect } from 'react';
import { MinusCircledIcon, PlusIcon } from '@radix-ui/react-icons';

type FormData = {
  actionType: TagAutomationActionType;
  criteria: {
    op: CriteriaOperator;
    value: string;
    type: CriteriaType;
  }[];
  programId: string;
  triggerType: TagAutomationTriggerType;
  tagIds: { label: string; value: string }[];
};
const getCriteriaType = (type: CriteriaType) => {
  switch (type) {
    case CriteriaType.ApplicationScore:
      return 'Application Score';
    default:
      return 'Unknown';
  }
};
export default function TagAutomationDialog({
  tagAutomation,
  onSuccess,
  isDialogOpen,
  setIsDialogOpen,
}: {
  tagAutomation?: TagAutomation;
  onSuccess: () => void;
  isDialogOpen: boolean;
  setIsDialogOpen: (open: boolean) => void;
}) {
  const { externalId, programs } = usePartner();
  const { showSnackbar } = useSnackbar();
  const [createTagAutomation, { error: createError, loading: creating }] = useMutation<
    { tagAutomation: { create: MutationResponse<TagAutomation> } },
    { input: CreateTagAutomationInput }
  >(CreateTagAutomation);
  const [updateTagAutomation, { error: mutationError, loading: submitting, reset: resetMutation }] =
    useMutation<
      { tagAutomation: { update: MutationResponse<TagAutomation> } },
      { input: UpdateTagAutomationInput }
    >(UpdateTagAutomation);

  const { data: partnerTagData, loading: tagsLoading } = useQuery<
    { partners: { partners: Partner[] } },
    { externalId: string }
  >(GetPartnerTags, {
    variables: { externalId },
  });

  const tags = partnerTagData?.partners?.partners?.[0]?.tags ?? [];

  const {
    control,
    register,
    handleSubmit,
    setValue,
    formState: { errors },
    reset: resetFormState,
  } = useForm<FormData>({
    defaultValues: {
      actionType: tagAutomation?.actionType ?? TagAutomationActionType.AddTag,
      criteria: [],
      programId: tagAutomation?.program?.id ?? 'all',
      triggerType: tagAutomation?.triggerType ?? TagAutomationTriggerType.ApplicationSubmitted,
      tagIds: [],
    },
  });

  const { fields, append, remove, replace } = useFieldArray({
    control,
    name: 'criteria',
    rules: {
      maxLength: 1,
    },
  });

  useEffect(() => {
    if (tagAutomation) {
      setValue('programId', tagAutomation.program?.id ?? 'all');
      setValue(
        'tagIds',
        tagAutomation.tags.map((tag) => ({ label: tag.name, value: tag.id })),
      );
      if (tagAutomation.criteria) {
        const allCriteria = [];
        for (const criteria of Object.keys(tagAutomation.criteria || {})) {
          const key = criteria as CriteriaType;
          const criteriaToAdd = {
            op: tagAutomation.criteria[key].op as CriteriaOperator,
            value: tagAutomation.criteria[key].value as string,
            type: criteria as CriteriaType,
          };
          allCriteria.push(criteriaToAdd);
        }
        replace(allCriteria);
      }
    }
  }, [tagAutomation, setValue, replace]);

  const onSubmit = async ({ ...variables }: FormData) => {
    try {
      const input = {
        ...variables,
        tagIds: variables.tagIds.map((tag) => tag.value),
        criteria: variables.criteria.reduce((acc, criteria) => {
          acc[criteria.type] = {
            op: criteria.op,
            value:
              criteria.op === CriteriaOperator.in
                ? String(criteria.value)
                    .split(',')
                    .map((value) => value.trim())
                : criteria.value,
          };
          return acc;
        }, {} as TagAutomationCriteria),
        programId: variables.programId === 'all' ? null : variables.programId,
      };
      if (tagAutomation) {
        const { data } = await updateTagAutomation({
          variables: {
            input: { ...input, id: tagAutomation.id },
          },
        });
        const { errors: responseErrors } = data?.tagAutomation?.update?.metadata || {};
        if (responseErrors?.length) {
          showSnackbar('Failed to update tag automation');
          throw new Error(responseErrors.join(', '));
        }
        showSnackbar('Tag automation successfully updated.');
      } else {
        const { data } = await createTagAutomation({
          variables: {
            input,
          },
        });
        const { errors: responseErrors } = data?.tagAutomation?.create?.metadata || {};
        if (responseErrors?.length) {
          showSnackbar('Failed to create tag automation');
          throw new Error(responseErrors.join(', '));
        }

        showSnackbar('Tag automation successfully created.');
      }

      resetFormState();
      onSuccess();
      setIsDialogOpen(false);
    } catch (e) {
      console.error(e);
    }
  };

  return (
    <Dialog.Root
      open={isDialogOpen}
      onOpenChange={(open) => {
        resetFormState();
        resetMutation();
        setIsDialogOpen(open);
      }}
    >
      <Dialog.Content size="4">
        <Dialog.Title>{tagAutomation ? 'Edit' : 'Create'} Tag Automation</Dialog.Title>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Flex direction="column" gap="3" mb="6">
            <Flex direction="column">
              <Text as="label" size="2" mb="1" weight="bold" htmlFor="programId">
                Program
              </Text>
              <Controller
                name="programId"
                control={control}
                render={({ field }) => (
                  <Select.Root
                    aria-invalid={!!errors.programId}
                    aria-describedby="error-programId"
                    onValueChange={field.onChange}
                    value={field.value}
                  >
                    <Select.Trigger placeholder="Select a program" />
                    <Select.Content position="popper">
                      <Select.Item key="all" value="all">
                        All
                      </Select.Item>
                      <Select.Separator />
                      {programs?.map((program) => (
                        <Select.Item key={program.id} value={program.id}>
                          {program.name}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select.Root>
                )}
              />
            </Flex>
            <Flex direction="column">
              <Text as="label" size="2" mb="1" weight="bold" htmlFor="triggerType">
                Trigger
              </Text>
              <Controller
                name="triggerType"
                control={control}
                rules={{ required: 'Trigger type is required' }}
                render={({ field }) => (
                  <Select.Root
                    aria-invalid={!!errors.triggerType}
                    aria-describedby="error-triggerType"
                    value={field.value}
                  >
                    <Select.Trigger placeholder="Select a trigger type" />
                    <Select.Content position="popper">
                      {Object.values(TagAutomationTriggerType).map((triggerType) => (
                        <Select.Item key={triggerType} value={triggerType}>
                          {getTriggerType(triggerType)}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select.Root>
                )}
              />

              <ErrorMessage
                errors={errors}
                name="name"
                render={({ message }) => (
                  <Text color="red" size="1" id="error-name" ml="1">
                    {message}
                  </Text>
                )}
              />
            </Flex>
            <Flex direction="column">
              <Text as="label" size="2" mb="1" weight="bold" htmlFor="actionType">
                Action
              </Text>
              <Controller
                name="actionType"
                control={control}
                rules={{ required: 'Action type is required' }}
                render={({ field }) => (
                  <Select.Root
                    aria-invalid={!!errors.actionType}
                    aria-describedby="error-actionType"
                    value={field.value}
                  >
                    <Select.Trigger placeholder="Select an action type" />
                    <Select.Content position="popper">
                      {Object.values(TagAutomationActionType).map((actionType) => (
                        <Select.Item key={actionType} value={actionType}>
                          {getActionType(actionType)}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select.Root>
                )}
              />
            </Flex>
            <Flex direction="column">
              <Text as="label" size="2" mb="1" weight="bold" htmlFor="tagIds">
                Tags
              </Text>
              <Controller
                name="tagIds"
                control={control}
                render={({ field }) => {
                  return (
                    <ReactSelect
                      {...field}
                      options={tags.map((tag) => ({ label: tag.name, value: tag.id }))}
                      isMulti
                      styles={{
                        control: (baseStyles, state) => ({
                          ...baseStyles,
                          fontSize: '14px',
                          option: {
                            fontSize: '14px',
                          },
                        }),
                      }}
                    />
                  );
                }}
              />
            </Flex>
            <Flex direction="column">
              <Flex gap="3" align="center" mb="3">
                <Text as="label" size="2" mb="1" weight="bold" htmlFor="criteria">
                  Criteria
                </Text>
                {fields.length !== 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      append({
                        op: CriteriaOperator.eq,
                        value: '',
                        type: CriteriaType.ApplicationScore,
                      });
                    }}
                  >
                    <PlusIcon />
                    Add Criteria
                  </Button>
                )}
              </Flex>
              <Flex direction="column" gap="2">
                {fields.map((field, index) => (
                  <Flex key={field.id} direction="row" gap="2">
                    <Controller
                      name={`criteria.${index}.type`}
                      control={control}
                      render={({ field }) => (
                        <Select.Root
                          aria-invalid={!!errors.criteria}
                          aria-describedby="error-criteria"
                          value={field.value}
                          onValueChange={(value) => {
                            setValue(`criteria.${index}.type`, value as CriteriaType);
                          }}
                        >
                          <Select.Trigger placeholder="Select a criteria type" />
                          <Select.Content position="popper">
                            {Object.values(CriteriaType).map((criteriaType) => (
                              <Select.Item key={criteriaType} value={criteriaType}>
                                {getCriteriaType(criteriaType)}
                              </Select.Item>
                            ))}
                          </Select.Content>
                        </Select.Root>
                      )}
                    />
                    <Controller
                      name={`criteria.${index}.op`}
                      control={control}
                      render={({ field }) => (
                        <Select.Root
                          aria-invalid={!!errors.criteria}
                          aria-describedby="error-criteria"
                          value={field.value}
                          onValueChange={(value) => {
                            setValue(`criteria.${index}.op`, value as CriteriaOperator);
                          }}
                        >
                          <Select.Trigger placeholder="Select a criteria operator" />{' '}
                          <Select.Content position="popper">
                            {Object.values(CriteriaOperator).map((operator) => (
                              <Select.Item key={operator} value={operator}>
                                {operator}
                              </Select.Item>
                            ))}
                          </Select.Content>
                        </Select.Root>
                      )}
                    />
                    <Controller
                      name={`criteria.${index}.value`}
                      control={control}
                      render={({ field }) => (
                        <Flex direction="column">
                          <TextField.Root
                            aria-invalid={!!errors.criteria?.[index]?.value}
                            aria-describedby="error-criteria"
                            value={field.value as string}
                            onChange={field.onChange}
                            required
                          />
                          <Text size="1" ml="1">
                            use comma separated list for multiple values
                          </Text>
                        </Flex>
                      )}
                    />
                    <Button
                      variant="soft"
                      color="red"
                      onClick={() => {
                        remove(index);
                      }}
                    >
                      <MinusCircledIcon />
                      Remove
                    </Button>
                  </Flex>
                ))}
              </Flex>
            </Flex>
            {mutationError && (
              <Callout.Root color="red" aria-live="polite">
                <Callout.Text highContrast>
                  Failed to create tag automation. If this problem persists, please contact Beam
                  support.
                </Callout.Text>
              </Callout.Root>
            )}
          </Flex>
          <Flex gap="2" justify="end">
            <Dialog.Close>
              <Button disabled={submitting} variant="soft">
                Cancel
              </Button>
            </Dialog.Close>
            <Button loading={submitting || creating} type="submit">
              Save
            </Button>
          </Flex>
        </form>
      </Dialog.Content>
    </Dialog.Root>
  );
}
