import {
  CreateTagAutomationInput,
  MutationResponse,
  Partner,
  TagAutomation,
  TagAutomationActionType,
  TagAutomationCriteria,
  TagAutomationTriggerType,
  UpdateTagAutomationInput,
} from '@bybeam/platform-types';
import CreateTagAutomation from './CreateTagAutomation.graphql';
import UpdateTagAutomation from './UpdateTagAutomation.graphql';
import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';
import { useMutation, useQuery } from '@apollo/client';
import GetPartnerTags from './GetPartnerTags.graphql';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import { ErrorMessage } from '@hookform/error-message';
import { Callout, Dialog, Flex, Text, Select, Button } from '@radix-ui/themes';
import { useForm, Controller } from 'react-hook-form';
import { getActionType, getTriggerType } from './TagAutomationsManager';
import ReactSelect from 'react-select';

type FormData = {
  actionType: TagAutomationActionType;
  criteria: TagAutomationCriteria;
  programId: string;
  triggerType: TagAutomationTriggerType;
  tagIds: string[];
};

export default function TagAutomationDialog({
  tagAutomation,
  onSuccess,
  isDialogOpen,
  setIsDialogOpen,
}: {
  tagAutomation?: TagAutomation;
  onSuccess: () => void;
  isDialogOpen: boolean;
  setIsDialogOpen: (open: boolean) => void;
}) {
  const { externalId } = usePartner();
  const { showSnackbar } = useSnackbar();
  const [createTagAutomation, { error: createError, loading: creating }] = useMutation<
    { tagAutomation: { create: MutationResponse<TagAutomation> } },
    { input: CreateTagAutomationInput }
  >(CreateTagAutomation);
  const [updateTagAutomation, { error: mutationError, loading: submitting, reset: resetMutation }] =
    useMutation<
      { tagAutomation: { update: MutationResponse<TagAutomation> } },
      { input: UpdateTagAutomationInput }
    >(UpdateTagAutomation);

  const { data: partnerTagData, loading: tagsLoading } = useQuery<
    { partners: { partners: Partner[] } },
    { externalId: string }
  >(GetPartnerTags, {
    variables: { externalId },
  });

  const tags = partnerTagData?.partners?.partners?.[0]?.tags ?? [];

  const {
    control,
    register,
    handleSubmit,
    formState: { errors },
    reset: resetFormState,
  } = useForm<FormData>({
    defaultValues: {
      actionType: tagAutomation?.actionType ?? TagAutomationActionType.AddTag,
      criteria: tagAutomation?.criteria ?? {},
      programId: tagAutomation?.programId ?? '',
      triggerType: tagAutomation?.triggerType ?? TagAutomationTriggerType.ApplicationSubmitted,
      tagIds: tagAutomation?.tags?.map((tag) => tag.id) ?? [],
    },
  });

  const onSubmit = async ({ ...variables }: FormData) => {
    try {
      if (tagAutomation) {
        const { data } = await updateTagAutomation({
          variables: {
            input: {
              ...variables,
              id: tagAutomation.id,
            },
          },
        });
        const { status: responseStatus, errors: responseErrors } =
          data?.tagAutomation?.update?.metadata || {};
        if (responseErrors?.length) {
          showSnackbar('Failed to update tag automation');
          throw new Error(responseErrors.join(', '));
        }
        showSnackbar('Tag automation successfully updated.');
      } else {
        await createTagAutomation({
          variables: {
            input: {
              ...variables,
            },
          },
        });
        showSnackbar('Tag automation successfully created.');
      }

      resetFormState();
      onSuccess();
      setIsDialogOpen(false);
    } catch (e) {
      console.error(e);
    }
  };

  return (
    <Dialog.Root
      open={isDialogOpen}
      onOpenChange={(open) => {
        resetFormState();
        resetMutation();
        setIsDialogOpen(open);
      }}
    >
      <Dialog.Content size="4">
        <Dialog.Title>{tagAutomation ? 'Edit' : 'Create'} Tag Automation</Dialog.Title>
        <Dialog.Description mb="4">
          <Callout.Root>
            <Callout.Text highContrast>
              Tag automations allow you to automatically add tags to cases based on certain
              criteria.
            </Callout.Text>
          </Callout.Root>
        </Dialog.Description>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Flex direction="column" gap="3" mb="6">
            <Flex direction="column">
              <Text as="label" size="2" mb="1" weight="bold" htmlFor="triggerType">
                Trigger type
              </Text>
              <Controller
                name="triggerType"
                control={control}
                rules={{ required: 'Trigger type is required' }}
                render={({ field }) => (
                  <Select.Root
                    aria-invalid={!!errors.triggerType}
                    aria-describedby="error-triggerType"
                    value={field.value}
                  >
                    <Select.Trigger placeholder="Select a trigger type" />
                    <Select.Content position="popper">
                      {Object.values(TagAutomationTriggerType).map((triggerType) => (
                        <Select.Item key={triggerType} value={triggerType}>
                          {getTriggerType(triggerType)}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select.Root>
                )}
              />

              <ErrorMessage
                errors={errors}
                name="name"
                render={({ message }) => (
                  <Text color="red" size="1" id="error-name" ml="1">
                    {message}
                  </Text>
                )}
              />
            </Flex>
            <Flex direction="column">
              <Text as="label" size="2" mb="1" weight="bold" htmlFor="actionType">
                Action type
              </Text>
              <Controller
                name="actionType"
                control={control}
                rules={{ required: 'Action type is required' }}
                render={({ field }) => (
                  <Select.Root
                    aria-invalid={!!errors.actionType}
                    aria-describedby="error-actionType"
                    value={field.value}
                  >
                    <Select.Trigger placeholder="Select an action type" />
                    <Select.Content position="popper">
                      {Object.values(TagAutomationActionType).map((actionType) => (
                        <Select.Item key={actionType} value={actionType}>
                          {getActionType(actionType)}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select.Root>
                )}
              />
            </Flex>
            <Flex direction="column">
              <Text as="label" size="2" mb="1" weight="bold" htmlFor="tagIds">
                Tags
              </Text>
              <Controller
                name="tagIds"
                control={control}
                render={({ field }) => {
                  return (
                    <ReactSelect
                      {...field}
                      options={tags.map((tag) => ({ label: tag.name, value: tag.id }))}
                      isMulti
                    />
                  );
                }}
              />
            </Flex>

            {/* <Flex direction="column">
              <Flex asChild align="center" gap="1">
                <Text as="label" size="2" mb="1" weight="bold" htmlFor="program-status">
                  Program Status
                  <Tooltip content="Create your program with a status of 'Closed' to prevent applications from being submitted too soon. You can change the status to 'Open' at any time.">
                    <InfoCircledIcon tabIndex={0} />
                  </Tooltip>
                </Text>
              </Flex>
              <Controller
                name="status"
                control={control}
                rules={{ required: 'Program status is required' }}
                render={({ field }) => (
                  <Select.Root onValueChange={field.onChange} defaultValue={ProgramStatus.Closed}>
                    <Select.Trigger
                      id="program-fund"
                      placeholder="Program status"
                      aria-invalid={!!errors.fundId}
                      aria-describedby="error-status"
                    />
                    <Select.Content position="popper">
                      {Object.values(ProgramStatus).map((statusOption) => (
                        <Select.Item key={statusOption} value={statusOption}>
                          {statusOption}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select.Root>
                )}
              /> */}
            {/* <ErrorMessage
                errors={errors}
                name="status"
                render={({ message }) => (
                  <Text color="red" size="1" id="error-status" ml="1">
                    {message}
                  </Text>
                )}
              />
            </Flex> */}
            {mutationError && (
              <Callout.Root color="red" aria-live="polite">
                <Callout.Text highContrast>
                  Failed to create program. If this problem persists, please contact Beam support.
                </Callout.Text>
              </Callout.Root>
            )}
          </Flex>
          <Flex gap="2" justify="end">
            <Button loading={submitting} type="submit">
              Save
            </Button>
            <Dialog.Close>
              <Button disabled={submitting} variant="soft">
                Cancel
              </Button>
            </Dialog.Close>
          </Flex>
        </form>
      </Dialog.Content>
    </Dialog.Root>
  );
}
