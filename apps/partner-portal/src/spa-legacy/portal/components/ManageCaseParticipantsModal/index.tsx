import useProgram from '@/spa-legacy/common/hooks/contexts/useProgram';
import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';
import {
  getAvailableApplicantTypes,
  getSortedApplicantTypes,
} from '@/spa-legacy/common/utilities/applicantType';
import {
  ApplicantTypeRole,
  type Case,
  type CaseParticipant,
  type ParticipantInput,
} from '@bybeam/platform-types';
import { useMemo, useState } from 'react';
import CaseParticipantsModal from './CaseParticipantsModal';
import ConfirmInviteModal from './ConfirmInviteModal';
import InviteParticipantModal from './InviteParticipantModal';
import InviteSentModal from './InviteSentModal';
import RemoveParticipantModal from './RemoveParticipantModal';
import useLinkingParticipant from './hooks/useLinkingParticipant';
import { getCaseParticipants } from './utils/columns';

type ManageCaseParticipantsProps = {
  onClose: () => void;
  case: Case;
};

enum ParticipantModals {
  RemoveParticipant = 'RemoveParticipant',
  InviteParticipant = 'InviteParticipant',
  ConfirmInvite = 'ConfirmInvite',
  InviteSent = 'InviteSent',
}

export default function ManageCaseParticipants({
  onClose,
  case: case_,
}: ManageCaseParticipantsProps) {
  const [openModal, setOpenModal] = useState<ParticipantModals>();
  const [selectedParticipant, setSelectedParticipant] = useState<CaseParticipant>();
  const [newParticipant, setNewParticipant] = useState<ParticipantInput>();

  const program = useProgram();
  const availableApplicantTypes = getAvailableApplicantTypes(program);
  const participants = getCaseParticipants(case_, availableApplicantTypes);
  const { inviteParticipant, unlinkParticipant, loading } = useLinkingParticipant(case_.id);
  const { showSnackbar } = useSnackbar();

  const onConfirmInvite = async () => {
    const success = await inviteParticipant(newParticipant);
    if (success) {
      setOpenModal(ParticipantModals.InviteSent);
      return;
    }
    showSnackbar('An error occurred while inviting participant, please contact support!');
  };

  const onRemoveParticipant = async () => {
    const success = await unlinkParticipant(selectedParticipant.id);
    if (success) {
      setOpenModal(undefined);
      setSelectedParticipant(null);
      return;
    }
    showSnackbar('An error occurred while removing participant, please contact support!');
  };

  const applicantTypes = useMemo(() => {
    const role =
      participants?.some((each) => each?.applicantType?.role === ApplicantTypeRole.FirstParty) &&
      ApplicantTypeRole.ThirdParty;
    return getSortedApplicantTypes(availableApplicantTypes, role);
  }, [availableApplicantTypes, participants]);

  switch (openModal) {
    case ParticipantModals.RemoveParticipant:
      return (
        <RemoveParticipantModal
          onClose={() => {
            setSelectedParticipant(null);
            setOpenModal(null);
          }}
          applicantTypes={availableApplicantTypes}
          participant={selectedParticipant}
          onRemoveParticipant={onRemoveParticipant}
        />
      );
    case ParticipantModals.InviteParticipant:
      return (
        <InviteParticipantModal
          onClose={() => {
            setNewParticipant(null);
            setOpenModal(null);
          }}
          onInviteParticipant={(formData: ParticipantInput) => {
            setNewParticipant(formData);
            setOpenModal(ParticipantModals.ConfirmInvite);
          }}
          defaultValues={newParticipant}
          applicantTypes={applicantTypes}
        />
      );
    case ParticipantModals.ConfirmInvite:
      return (
        <ConfirmInviteModal
          newParticipant={newParticipant}
          onClose={() => {
            setOpenModal(ParticipantModals.InviteParticipant);
          }}
          onConfirmInvite={onConfirmInvite}
          preventClose={loading}
          applicantTypes={applicantTypes}
        />
      );
    case ParticipantModals.InviteSent:
      return (
        <InviteSentModal
          participant={participants?.find(
            (each) =>
              each.applicantType.id === newParticipant.applicantTypeId &&
              each.email === newParticipant.email &&
              each.name === newParticipant.name,
          )}
          autoLink={newParticipant?.autoLink}
          onClose={onClose}
          onBack={() => {
            setNewParticipant(null);
            setOpenModal(null);
          }}
        />
      );
    default:
      return (
        <CaseParticipantsModal
          onClose={onClose}
          participants={participants}
          onRemoveParticipant={(participant: CaseParticipant) => {
            setOpenModal(ParticipantModals.RemoveParticipant);
            setSelectedParticipant(participant);
          }}
          onInviteParticipant={() => setOpenModal(ParticipantModals.InviteParticipant)}
        />
      );
  }
}
