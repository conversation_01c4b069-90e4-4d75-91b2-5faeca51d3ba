import useForm, {
  ValidationMode,
  emailField,
  fieldUpdate,
  optionalField,
  requiredField,
} from '@/app/hooks/useForm';
import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';
import Button from '@/spa-legacy/common/components/Button';
import Checkbox from '@/spa-legacy/common/components/Checkbox';
import Dropdown from '@/spa-legacy/common/components/Dropdown';
import Message from '@/spa-legacy/common/components/Message';
import Modal from '@/spa-legacy/common/components/Modal';
import ModalActions from '@/spa-legacy/common/components/Modal/ModalActions';
import TextInput from '@/spa-legacy/common/components/TextInput';
import Typography from '@/spa-legacy/common/components/Typography';
import type { ApplicantType, ParticipantInput } from '@bybeam/platform-types';
import cx from 'classnames';
import { usePostHog } from 'posthog-js/react';

type InviteParticipantProps = {
  onClose: () => void;
  onInviteParticipant: (formData: ParticipantInput) => void;
  defaultValues?: ParticipantInput;
  applicantTypes: ApplicantType[];
};

export default function InviteParticipantModal({
  onClose,
  onInviteParticipant,
  defaultValues = {
    email: '',
    name: '',
    applicantTypeId: '',
    autoLink: false,
  },
  applicantTypes,
}: InviteParticipantProps) {
  const posthog = usePostHog();
  const isAutoLinkParticipantEnabled = posthog.isFeatureEnabled(
    POSTHOG_FEATURE_FLAGS.autoLinkParticipant,
  );
  const { formData, dispatch, errors, trySubmit } = useForm(
    {
      ...defaultValues,
      applicantTypeId:
        !defaultValues?.applicantTypeId && applicantTypes?.length === 1
          ? applicantTypes?.[0]?.id
          : defaultValues?.applicantTypeId,
    },
    {
      email: emailField(),
      name: requiredField(),
      applicantTypeId: requiredField(),
      autoLink: optionalField(),
    },
    ValidationMode.RequiredOnSubmit,
  );

  return (
    <Modal title="Invite Participant" isOpen onClickClose={onClose} size="M">
      <div className="flex flex-col gap-6">
        <Typography variant="body">Provide the new participant’s contact information.</Typography>
        <div
          className={cx('grid gap-8 content-center grid-cols-2', {
            'grid-cols-3': applicantTypes?.length > 1,
          })}
        >
          {applicantTypes?.length > 1 && (
            <Dropdown
              id="applicantTypeId"
              label="Applicant Type"
              items={applicantTypes.map((applicantType) => ({
                label: applicantType.name,
                value: applicantType.id,
              }))}
              value={formData.applicantTypeId}
              onChange={(value): void => dispatch(fieldUpdate('applicantTypeId', value as string))}
              error={!!errors.applicantTypeId}
              helperText={errors.applicantTypeId as string}
              required
              searchable={false}
            />
          )}
          <TextInput
            id="name"
            label="Name"
            value={formData.name}
            onChange={(value): void => dispatch(fieldUpdate('name', value))}
            error={!!errors.name}
            required
            helperText={errors.name as string}
          />
          <TextInput
            id="email"
            label="Email Address"
            value={formData.email}
            onChange={(value): void => dispatch(fieldUpdate('email', value))}
            error={!!errors.email}
            required
            helperText={errors.email as string}
          />
        </div>
        {isAutoLinkParticipantEnabled && (
          <div className="w-full">
            <Checkbox
              checked={formData.autoLink ?? false}
              onClick={(): void => dispatch(fieldUpdate('autoLink', !formData.autoLink))}
              label={
                <Typography variant="body" tag="span">
                  Automatically link the participant to this case without requiring them to enter a
                  link code
                </Typography>
              }
            />
          </div>
        )}
        <Message variant="info">
          {formData.autoLink ? (
            <>
              A unique link will be sent to the contact information provided which automatically
              links the participant to this case without requiring them to enter a link code.
            </>
          ) : (
            <>
              A unique link code will be sent to the contact information provided. This will allow
              the new participant to link to this case via their portal.
            </>
          )}
        </Message>
      </div>
      <ModalActions>
        <Button type="submit" shrink onClick={() => trySubmit(() => onInviteParticipant(formData))}>
          Invite Participant
        </Button>
        <Button type="button" onClick={onClose} variant="outline" shrink>
          Back
        </Button>
      </ModalActions>
    </Modal>
  );
}
