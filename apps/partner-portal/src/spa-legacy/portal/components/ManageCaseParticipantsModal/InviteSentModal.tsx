import Button from '@/spa-legacy/common/components/Button';
import FieldDetail from '@/spa-legacy/common/components/FieldDetail';
import FieldDetailList from '@/spa-legacy/common/components/FieldDetail/FieldDetailList';
import Message from '@/spa-legacy/common/components/Message';
import Modal from '@/spa-legacy/common/components/Modal';
import ModalActions from '@/spa-legacy/common/components/Modal/ModalActions';
import type { CaseParticipant } from '@bybeam/platform-types';
import TruncatedTextTooltip from '../TruncatedTextTooltip';

type InviteParticipantProps = {
  onClose: () => void;
  onBack: () => void;
  participant: CaseParticipant;
  autoLink?: boolean;
};

export default function InviteSentModal({
  onClose,
  onBack,
  participant,
  autoLink,
}: InviteParticipantProps) {
  return (
    <Modal title="Invite Sent" isOpen onClickClose={onClose} size="M">
      <div className="flex flex-col gap-6">
        <FieldDetailList columns={3}>
          <FieldDetail label="Name">
            <div className="truncate">
              <TruncatedTextTooltip>{participant?.name}</TruncatedTextTooltip>
            </div>
          </FieldDetail>
          <FieldDetail label="Email Address">
            <div className="truncate">
              <TruncatedTextTooltip>{participant?.email}</TruncatedTextTooltip>
            </div>
          </FieldDetail>
          <FieldDetail label="Link Code">{participant?.invitationCodes?.[0]?.code}</FieldDetail>
        </FieldDetailList>
        <Message variant="success">
          {autoLink ? (
            <>
              A link has been sent to the new participant via email. The{' '}
              {participant?.applicantType?.name?.toLowerCase()} has received a confirmation email.
            </>
          ) : (
            <>
              A link code has been sent to the new participant via email. The{' '}
              {participant?.applicantType?.name?.toLowerCase()} has received a confirmation email
            </>
          )}
        </Message>
      </div>
      <ModalActions>
        <Button type="submit" shrink onClick={onBack}>
          View Case Participants
        </Button>
        <Button type="button" onClick={onClose} variant="outline" shrink>
          Exit
        </Button>
      </ModalActions>
    </Modal>
  );
}
