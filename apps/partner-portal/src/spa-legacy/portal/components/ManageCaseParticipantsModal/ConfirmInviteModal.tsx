import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';
import Button from '@/spa-legacy/common/components/Button';
import CheckboxComponent from '@/spa-legacy/common/components/Checkbox';
import FieldDetail from '@/spa-legacy/common/components/FieldDetail';
import FieldDetailList from '@/spa-legacy/common/components/FieldDetail/FieldDetailList';
import Message from '@/spa-legacy/common/components/Message';
import Modal from '@/spa-legacy/common/components/Modal';
import ModalActions from '@/spa-legacy/common/components/Modal/ModalActions';
import Typography from '@/spa-legacy/common/components/Typography';
import type { ApplicantType, ParticipantInput } from '@bybeam/platform-types';
import { usePostHog } from 'posthog-js/react';
import TruncatedTextTooltip from '../TruncatedTextTooltip';

type InviteParticipantProps = {
  onClose: () => void;
  onConfirmInvite: () => void;
  newParticipant?: ParticipantInput;
  preventClose: boolean;
  applicantTypes: ApplicantType[];
};

export default function ConfirmInviteModal({
  onClose,
  onConfirmInvite,
  newParticipant,
  preventClose,
  applicantTypes,
}: InviteParticipantProps) {
  const hasMultiParties = applicantTypes?.length > 1;
  const posthog = usePostHog();
  const isAutoLinkParticipantEnabled = posthog.isFeatureEnabled(
    POSTHOG_FEATURE_FLAGS.autoLinkParticipant,
  );
  return (
    <Modal
      title="Confirm Invite"
      isOpen
      onClickClose={onClose}
      size="M"
      preventClose={preventClose}
    >
      <div className="flex flex-col gap-6">
        <Typography variant="body">
          If this information is correct, select Confirm Invite. To make any changes, select Back.
        </Typography>
        <FieldDetailList columns={hasMultiParties ? 3 : 2}>
          {hasMultiParties && (
            <FieldDetail label="Applicant Type">
              {applicantTypes?.find((type) => type.id === newParticipant?.applicantTypeId)?.name ??
                '-'}
            </FieldDetail>
          )}
          <FieldDetail label="Name">
            <div className="truncate">
              <TruncatedTextTooltip>{newParticipant?.name}</TruncatedTextTooltip>
            </div>
          </FieldDetail>
          <FieldDetail label="Email Address">
            <div className="truncate">
              <TruncatedTextTooltip>{newParticipant?.email}</TruncatedTextTooltip>
            </div>
          </FieldDetail>
        </FieldDetailList>
        {isAutoLinkParticipantEnabled && (
          <div className="w-full">
            <CheckboxComponent
              checked={newParticipant?.autoLink ?? false}
              label={
                <Typography variant="body" tag="span">
                  Automatically link the participant to this case without requiring them to enter a
                  link code
                </Typography>
              }
              readonly
            />
          </div>
        )}
        <Message variant="warning">
          {newParticipant?.autoLink ? (
            <>
              You are sending a link to <strong>{newParticipant?.email}</strong> which automatically
              links the participant to this case without requiring them to enter a link code. Please
              make sure the email address is correct.
            </>
          ) : (
            <>
              You are sending a link code to <strong>{newParticipant?.email}</strong>. The link code
              is unique to this case and should be treated confidentially. Please make sure the
              email address is correct.
            </>
          )}
        </Message>
      </div>
      <ModalActions>
        <Button type="submit" shrink onClick={onConfirmInvite}>
          Confirm Invite
        </Button>
        <Button type="button" onClick={onClose} variant="outline" shrink>
          Back
        </Button>
      </ModalActions>
    </Modal>
  );
}
