{"name": "@bybeam/verification-server", "version": "0.0.1", "private": true, "main": "dist/index.js", "type": "module", "types": "dist/index.d.ts", "engines": {"node": "20.10.0", "npm": ">=8.19.2"}, "scripts": {"build": "tsc --build", "grpc:repl": "evans --proto proto/verification.proto repl", "start:prod": "node ./dist/index.js", "start:dev": "tsc-watch --build --onSuccess 'node ./dist/index.js'", "test": "vitest"}, "devDependencies": {"@types/node": "22.0.0", "@types/temp": "0.9.4", "@vitest/coverage-v8": "2.1.9", "dotenv": "16.4.5", "ts-node": "10.9.2", "tsc-watch": "7.2.0", "typescript": "5.5.4", "vitest": "2.1.9"}, "dependencies": {"@bybeam/infrastructure-lib": "workspace:*", "@bybeam/platform-lib": "workspace:*", "@bybeam/verification-client": "workspace:*", "@bybeam/verification-types": "workspace:^", "@google-cloud/storage": "7.12.1", "@grpc/grpc-js": "1.10.10", "@grpc/proto-loader": "0.7.10", "@nestjs/core": "^10.3.5", "axios": "1.12.2", "csv-parse": "5.5.3", "dayjs": "1.11.11", "google-proto-files": "4.0.0", "google-protobuf": "3.21.2", "grpc-js-health-check": "1.2.2", "pg": "8.13.1", "reflect-metadata": "0.1.14", "temp": "0.9.4", "typeorm": "0.3.17"}}