{"name": "@bybeam/identity-server", "version": "0.0.0", "private": true, "type": "module", "scripts": {"build": "nest build", "emulator:seed": "nest build && node --loader ts-node/esm scripts/seedEmulator.ts", "nest": "nest", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "test": "vitest"}, "devDependencies": {"@nestjs/cli": "^10.3.2", "@nestjs/schematics": "^10.0.3", "@nestjs/testing": "^10.3.0", "@tsconfig/node20": "20.1.4", "@types/node": "22.0.0", "@types/uuid": "9.0.8", "@vitest/coverage-v8": "2.1.9", "mockdate": "3.0.5", "source-map-support": "0.5.21", "ts-loader": "^9.5.1", "ts-node": "10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "5.5.4", "unplugin-swc": "^1.4.5", "vitest": "2.1.9"}, "dependencies": {"@authzed/authzed-node": "^0.15.0", "@bybeam/formatting": "workspace:^", "@bybeam/identity-client": "workspace:^", "@bybeam/identity-entities": "workspace:^", "@bybeam/infrastructure-lib": "workspace:^", "@bybeam/notification-client": "workspace:^", "@bybeam/platform-entities": "workspace:^", "@bybeam/platform-lib": "workspace:^", "@bybeam/platform-types": "workspace:^", "@bybeam/verification-client": "workspace:^", "@google-cloud/recaptcha-enterprise": "5.1.0", "@grpc/grpc-js": "1.10.10", "@grpc/proto-loader": "0.7.10", "@nestjs/common": "^11.0.12", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.3.0", "@nestjs/jwt": "^10.2.0", "@nestjs/microservices": "^10.4.6", "@nestjs/terminus": "^11.0.0", "@nestjs/typeorm": "^11.0.0", "argon2": "^0.44.0", "dayjs": "1.11.11", "firebase": "^10.8.0", "firebase-admin": "^12.0.0", "grpc-health-check": "^2.0.0", "nestjs-grpc-reflection": "^0.2.2", "nestjs-pino": "^3.5.0", "pg": "8.13.1", "reflect-metadata": "0.1.14", "rxjs": "^7.8.1", "typeorm": "0.3.17"}}