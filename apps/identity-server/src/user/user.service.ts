import { randomBytes } from 'node:crypto';
import {
  AuthenticationMechanism,
  CreateUserRequest,
  EntityType,
  GetAdminsResponse,
  GetCoreUsersRequest,
  GetUserRequest,
  IsValidRole,
  ReadPortalRolesRequest,
  ReadPortalRolesResponse,
  Relation,
  ResetPasswordRequest,
  SendMagicLinkRequest,
  SendVerificationEmailRequest,
  SessionRequest,
  TenantRole,
  UpdateUserRequest,
  User,
  UserResponse,
  UserRole,
  VerifyEmailRequest,
} from '@bybeam/identity-client/types';
import { AdvocateEntity, AuditLogEntity, UserEntity } from '@bybeam/identity-entities';
import { NotificationType } from '@bybeam/notification-client';
import { UserEntity as CoreUserEntity, PartnerEntity } from '@bybeam/platform-entities';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import { omit, pick } from '@bybeam/platform-lib/utilities/set';
import { FeatureName, Partner } from '@bybeam/platform-types';
import { status } from '@grpc/grpc-js';
import { Status } from '@grpc/grpc-js/build/src/constants.js';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RpcException } from '@nestjs/microservices';
import { InjectRepository } from '@nestjs/typeorm';
import { DecodedIdToken } from 'firebase-admin/auth';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import { In, Not, Repository } from 'typeorm';
import { ILike } from 'typeorm/find-options/operator/ILike.js';
import { AdminService } from '../admin/admin.service.js';
import { AuthorizationService } from '../authorization/authorization.service.js';
import { Database } from '../database/database.js';
import { NotificationService } from '../notification/notification.service.js';
import { mapGCIPToIdentityUser } from '../utils/mapping.js';
import { getTenantRole, resolveTenantId } from '../utils/tenants.js';
import { Action, transaction } from '../utils/transaction.js';
import { transformToIdentityUser } from '../utils/transformToIdentityUser.js';
import { VerificationService } from '../verification/verification.service.js';
import { PermissionsService } from './permissions.service.js';
import { ProvisionService } from './provision.service.js';

type ValidateByTokenInput = SessionRequest & { sessionId: string };

// TODO refactor this
// user crud
// email stuff
// admin vs applicant gets
@Injectable()
export class UserService {
  constructor(
    @InjectPinoLogger(UserService.name) private readonly logger: PinoLogger,
    private admin: AdminService,
    private authZ: AuthorizationService,
    private config: ConfigService,
    private notification: NotificationService,
    private permissions: PermissionsService,
    private provision: ProvisionService,
    private verificationService: VerificationService,
    @InjectRepository(UserEntity, Database.Identity)
    private identityUserRepo: Repository<UserEntity>,
    @InjectRepository(AdvocateEntity, Database.Identity)
    private advocateRepo: Repository<AdvocateEntity>,
    @InjectRepository(PartnerEntity, Database.Core)
    private partnerRepo: Repository<PartnerEntity>,
    @InjectRepository(CoreUserEntity, Database.Core)
    private coreUserRepo: Repository<CoreUserEntity>,
    @InjectRepository(AuditLogEntity, Database.Identity)
    private auditLogRepo: Repository<AuditLogEntity>,
  ) {}

  private validateCreateUserInput({
    roles,
    ...input
  }: CreateUserRequest): Omit<CreateUserRequest, 'roles'> & { roles: UserRole[] } {
    if (!roles?.every((role: string) => IsValidRole(role)))
      throw new Error(`invalid roles ${roles.join(', ')}`);
    return { ...input, roles };
  }

  async createUser(input: CreateUserRequest) {
    this.logger.debug({ input }, 'create user request');
    const { partnerId, roles } = this.validateCreateUserInput(input);

    let email = input?.email?.toLowerCase()?.trim();
    if (!email) {
      email = `${randomBytes(8).toString('hex')}@generated.bybeam.co`;
      this.logger.warn(`auto generated email ${email}`);
    }

    const userToCreate = {
      email,
      name: input?.name || email,
      phone: input?.phone,
      verifiedEmail: false,
    };

    const partner = await this.partnerRepo.findOne({
      where: { id: partnerId },
      relations: ['features'],
    });
    const tenantId = resolveTenantId({
      configService: this.config,
      partnerConfig: partner?.config,
      role: roles.includes(Relation.Applicant) ? 'applicant' : 'advocate',
    });

    const gcipUser = await this.provision.provisionGCIPUser({
      user: userToCreate,
      tenantId,
    });
    const gcipUid = gcipUser.uid;

    const { userId } = await this.provision.provisionUser({
      user: userToCreate,
      roles,
      partnerId,
      tenantId,
      gcipUid,
      applicantTypeId: input.applicantTypeId,
      sudoRequest: true,
    });

    const user = await this.identityUserRepo.findOne({
      where: { id: userId },
      relations: ['advocates', 'applicants'],
    });

    if (checkFeature(partner?.features, FeatureName.UserValidation)) {
      await this.verificationService.validateUser(user);
    }

    return {
      message: 'successfully created user',
      id: user.id,
      applicants: user.applicants,
      advocates: user.advocates,
    };
  }

  async getAdmins(input: GetCoreUsersRequest): Promise<GetAdminsResponse> {
    const [advocates, count] = await this.advocateRepo.findAndCount({
      where: { coreUserId: In(input.ids) },
      relations: ['user'],
    });
    return {
      admins: advocates.map((adv) => ({
        ...transformToIdentityUser(adv.user),
        coreUserId: adv.coreUserId,
      })),
      count,
    };
  }

  async getUser(input: GetUserRequest): Promise<UserResponse> {
    let user: UserEntity;
    switch (input.userIdentifier) {
      case 'coreUserId':
        user = await this.identityUserRepo.findOne({
          where: [
            { applicants: { id: input.coreUserId } },
            { advocates: { coreUserId: input.coreUserId } },
          ],
        });
        break;
      case 'gcipUid':
        user = await this.identityUserRepo.findOne({
          where: {
            gcipUsers: { uid: input.gcipUid },
          },
        });
        break;
      case 'identityUserId':
        user = await this.identityUserRepo.findOne({ where: { id: input.identityUserId } });
        break;
    }
    return {
      message: user ? 'User found' : `No user found for input ${JSON.stringify(input)}`,
      user: user
        ? { id: user.id, name: user.name, email: user.email, zedToken: user.zedToken }
        : null,
      _user: 'user',
    };
  }

  private async syncGcipAndIdentityUsers(
    gcipUserEmail: string,
    identityUser: User,
    partnerId: string,
  ) {
    if (gcipUserEmail !== identityUser.email) {
      await this.updateUser({
        email: gcipUserEmail,
        partnerId: partnerId,
        roles: [],
        identityUserId: identityUser.id,
        _userId: 'userId',
        _tenantId: 'tenantId',
        _email: 'email',
        _name: 'name',
        _phone: 'phone',
        userIdentifier: 'identityUserId',
        _gcipUid: 'gcipUid',
      });
    }
  }

  async validateByEmail(input: SendMagicLinkRequest): Promise<User> {
    const { tenantId, partnerId } = input;
    const email = input.email.toLowerCase();
    const role = getTenantRole(tenantId);
    const user = await this.identityUserRepo.findOne({
      where: { email: ILike(email) },
      relations: ['applicants', 'advocates', 'gcipUsers'],
    });

    const gcipUser = await this.provision.provisionGCIPUser({
      user: { email, name: email, verifiedEmail: false },
      tenantId,
    });
    const gcipUid = gcipUser.uid;

    const realIdentityUser = await this.identityUserRepo.findOne({
      where: { gcipUsers: { uid: gcipUid } },
      relations: ['gcipUsers'],
    });
    if (gcipUser && realIdentityUser) {
      await this.syncGcipAndIdentityUsers(gcipUser.email, realIdentityUser, partnerId);
    }
    // This supposed to drop after investigation is completed
    if (gcipUid) {
      const userWithSameGCIP = await this.findByGCIPUID(gcipUid);
      if (!!userWithSameGCIP && (!user || user?.id !== userWithSameGCIP.id)) {
        this.logger.warn(`The GCIP ID ${gcipUid} belongs to another user ${userWithSameGCIP.id}`);
      }
    }

    if (!user) {
      if (role !== 'applicant') throw new Error(`cannot provision ${role} user without token`);
      await this.provision.provisionUser({
        user: { email, name: email, verifiedEmail: false },
        partnerId,
        tenantId,
        gcipUid,
      });
    }

    if (user) await this.permissions.syncPermissions({ user, partnerId, tenantId, gcipUid });

    return this.identityUserRepo.findOne({
      where: { email: ILike(email) },
      relations: ['applicants', 'advocates', 'gcipUsers'],
    });
  }

  private async validateSSOProvision(
    partner: Partner,
    gcipUser: DecodedIdToken,
    authenticationMechanism: AuthenticationMechanism,
    tenantId: string,
  ) {
    if (authenticationMechanism !== 'SAML') {
      return { availableSeats: false, validSAMLAssertion: false };
    }

    const samlConfig = partner?.config?.identity?.advocate?.saml;

    let validSAMLAssertion = false;
    let availableSeats = false;

    const partnerSAMLProviderId = samlConfig?.providerId ?? 'unknown_config_provider_id';
    const firebaseSignProviderId =
      gcipUser?.firebase?.sign_in_provider ?? 'unknown_sign_in_provider';

    if (
      tenantId === this.config.get('BEAM_TENANT_ID') &&
      firebaseSignProviderId === this.config.get('BEAM_PROVIDER_ID')
    ) {
      this.logger.info(
        { firebaseSignProviderId, tenantId, gcipUser, partner },
        'bypassing seat restriction for Beam internal tenant/provider',
      );
      return { availableSeats: true, validSAMLAssertion: true };
    }

    if (partnerSAMLProviderId === firebaseSignProviderId) {
      validSAMLAssertion = true;
    }

    if (!validSAMLAssertion) {
      throw new RpcException({
        code: status.PERMISSION_DENIED,
        message: `the SAML assertion has been deemed invalid, configured provider: '${partnerSAMLProviderId}' signin provider: '${firebaseSignProviderId}'`,
      });
    }

    // Naive seat limit implementation. This requires a setup in GCIP with a distinct tenant
    // for the partner with a single provider for the SAML integration, so we can count the
    // number of users for the tenant.
    const [_, filledSeats] = await this.advocateRepo.findAndCount({
      relations: ['user', 'user.gcipUsers'],
      where: { partnerId: partner.id, user: { gcipUsers: { tenantId } } },
    });
    const maxSeats = samlConfig?.seats;
    if (!maxSeats || filledSeats < maxSeats) {
      availableSeats = true;
    }

    if (!availableSeats) {
      throw new RpcException({
        code: status.PERMISSION_DENIED,
        message: `exceeded available seats, ${filledSeats} of ${maxSeats} are filled`,
      });
    }

    this.logger.info(
      {
        firebaseSignProviderId,
        partnerSAMLProviderId,
        authenticationMechanism,
        tenantId,
        maxSeats,
        filledSeats: filledSeats + 1,
      },
      'creating advocate based on SAML assertion',
    );

    return { availableSeats, validSAMLAssertion };
  }

  async validateByToken(input: ValidateByTokenInput): Promise<User> {
    const { tenantId, partnerId, sessionId, authenticationMechanism } = input;
    const gcipUser = await this.admin.verifyToken(input.token, tenantId);
    const partner = await this.partnerRepo.findOne({ where: { id: input.partnerId } });
    const user = await this.identityUserRepo.findOne({
      where: { gcipUsers: [{ uid: gcipUser.uid }] },
      relations: ['applicants', 'advocates', 'gcipUsers'],
    });

    const { availableSeats, validSAMLAssertion } = await this.validateSSOProvision(
      partner,
      gcipUser,
      authenticationMechanism,
      tenantId,
    );
    const shouldVerifyEmail =
      user && !user.verifiedEmail && input.authenticationMechanism === 'EMAIL_TOKEN';

    if (gcipUser && user) {
      await this.syncGcipAndIdentityUsers(gcipUser.email, user, partnerId); // Only sync if user exists
    }
    if (!user)
      await this.provision.provisionUser({
        user: mapGCIPToIdentityUser(gcipUser),
        partnerId,
        tenantId,
        gcipUid: gcipUser.uid,
        sessionId,
        sudoRequest: availableSeats && validSAMLAssertion,
      });
    else if (user)
      await this.permissions.syncPermissions({
        user,
        partnerId,
        tenantId,
        gcipUid: gcipUser.uid,
        sessionId,
      });

    if (shouldVerifyEmail) await this.verifyEmail({ token: input.token, tenantId });

    return this.findByGCIPUID(gcipUser.uid);
  }

  async verifyEmail(request: VerifyEmailRequest) {
    const gcipUser = await this.admin.verifyToken(request.token, request.tenantId);

    if (!gcipUser?.email_verified) await this.admin.verifyEmail(gcipUser.uid, request.tenantId);

    const user = await this.findByGCIPUID(gcipUser.uid);

    if (!user)
      throw new RpcException({
        status: Status.NOT_FOUND,
        message: 'user not found in identity DB',
      });
    await this.identityUserRepo.update({ id: user.id }, { verifiedEmail: true });

    const coreUsers = [
      ...user.advocates.map(({ coreUserId }) => coreUserId),
      ...user.applicants.map(({ id }) => id),
    ].filter(Boolean);
    if (coreUsers.length)
      await this.coreUserRepo.update({ id: In(coreUsers) }, { validatedEmail: true });

    await this.auditLogRepo.save({
      entityId: user.id,
      entityType: EntityType.IdentityUser,
      sessionId: gcipUser?.beam?.sessionId,
      details: { message: 'verified email address', value: user.email },
    });

    return { message: 'email verified' };
  }

  async findByGCIPUID(uid: string) {
    return this.identityUserRepo.findOne({
      where: { gcipUsers: [{ uid }] },
      relations: ['applicants', 'advocates', 'gcipUsers'],
    });
  }

  private findUserByCoreId(id: string, role: TenantRole) {
    switch (role) {
      case 'advocate':
        return this.identityUserRepo.findOne({
          where: { advocates: [{ coreUserId: id }] },
          relations: ['applicants', 'advocates', 'gcipUsers'],
        });
      case 'applicant':
        return this.identityUserRepo.findOne({
          where: { applicants: [{ id: id }] },
          relations: ['applicants', 'advocates', 'gcipUsers'],
        });
      default:
        return;
    }
  }

  private async findUserOrThrow(input: UpdateUserRequest): Promise<User> {
    const { userIdentifier, coreUserId } = input;
    let identityUserId = input.identityUserId;
    let user: User;

    if (userIdentifier === 'coreUserId' && coreUserId) {
      user = await this.identityUserRepo.findOne({
        where: [{ applicants: { id: coreUserId } }, { advocates: { coreUserId } }],
      });
      identityUserId = user?.id;
    }

    if (identityUserId)
      user = await this.identityUserRepo.findOne({
        where: { id: identityUserId },
        relations: ['applicants', 'advocates', 'gcipUsers'],
      });
    if (!user)
      throw new RpcException({
        status: Status.NOT_FOUND,
        message: `user not found with id ${JSON.stringify({ identityUserId, coreUserId })}`,
      });

    const partnerIds = [
      ...new Set([
        ...user.advocates.map((adv) => adv.partnerId),
        ...user.applicants.map((app) => app.partnerId),
      ]),
    ];

    if (partnerIds.length > 1) {
      this.logger.warn({ partnerIds }, `User ${user.id} belongs to multi partners`);
    }

    return user;
  }

  private async validateAndMap(user: User, input: UpdateUserRequest) {
    const { email: unsanitizedEmail, phone, name, partnerId } = input;
    const email = unsanitizedEmail?.toLocaleLowerCase();

    const isPhoneChanged = 'phone' in input && user.phone !== phone;
    const isEmailChanged = 'email' in input && user.email !== email;
    const isNameChanged = 'name' in input && user.name !== name;

    let duplicatePhone = false;
    if (isPhoneChanged && !!phone)
      duplicatePhone = await this.identityUserRepo.exist({ where: { phone, id: Not(user.id) } });
    if (duplicatePhone)
      this.logger.warn({ phone }, 'phone number already exists, leaving out of update');

    const coreUsers = [
      ...user.advocates.map(({ coreUserId }) => coreUserId),
      ...user.applicants.map(({ id }) => id),
    ].filter(Boolean);

    let duplicateEmail = false;
    if (isEmailChanged && !!email)
      duplicateEmail = await this.coreUserRepo.exist({
        where: {
          email,
          partnerId,
          ...(!!coreUsers?.length && { id: Not(In(coreUsers)) }),
        },
      });
    if (duplicateEmail)
      throw new RpcException({
        status: Status.ALREADY_EXISTS,
        message: `a user exists with email: ${email}`,
      });

    return {
      identityUpdate: {
        ...(isEmailChanged && { email, verifiedEmail: false }),
        ...(isNameChanged && { name }),
        ...(isPhoneChanged && { phone: phone ? (!duplicatePhone ? phone : user.phone) : null }),
      },
      coreUpdate: {
        ...(isEmailChanged && { email, validatedEmail: false }),
        ...(isNameChanged && { name }),
        ...(isPhoneChanged && { phone: phone || null }),
      },
    };
  }

  async updateUser(input: UpdateUserRequest) {
    const user = await this.findUserOrThrow(input);
    const { identityUpdate, coreUpdate } = await this.validateAndMap(user, input);

    this.logger.info({ identityUpdate, coreUpdate }, `Update user ${user.id} info`);

    const reverseUpdate = pick(user, ['email', 'name', 'phone', 'verifiedEmail']) as Pick<
      User,
      'email' | 'name' | 'phone' | 'verifiedEmail'
    >;

    const coreUsers = [
      ...user.advocates.map(({ coreUserId }) => coreUserId),
      ...user.applicants.map(({ id }) => id),
    ].filter(Boolean);

    const actions: Action[] = [];

    if (Object.keys(identityUpdate)) {
      actions.push(
        {
          run: () => this.identityUserRepo.update(user.id, identityUpdate),
          revert: () => this.identityUserRepo.update(user.id, reverseUpdate),
        },
        ...user.gcipUsers.map(({ uid, tenantId }) => ({
          run: () => this.admin.updateUser(identityUpdate, tenantId, uid),
          revert: () => this.admin.updateUser(reverseUpdate, tenantId, uid),
        })),
      );
    }

    if (Object.keys(coreUpdate) && coreUsers.length) {
      actions.push({
        run: () => this.coreUserRepo.update({ id: In(coreUsers) }, coreUpdate),
        revert: () =>
          this.coreUserRepo.update(
            { id: In(coreUsers) },
            {
              ...omit(reverseUpdate, ['verifiedEmail']),
              validatedEmail: reverseUpdate.verifiedEmail,
            },
          ),
      });
    }

    if (actions?.length) await transaction({ actions, logger: this.logger, unwind: false });

    if (user?.advocates?.length && input.roles?.length) {
      await this.authZ.upsertPortalRoles({
        partnerId: input.partnerId,
        userId: user.id,
        roles: input.roles,
      });
    }

    const partner = await this.partnerRepo.findOne({
      where: { id: input.partnerId },
      relations: ['features'],
    });
    if (checkFeature(partner?.features, FeatureName.UserValidation)) {
      await this.verificationService.validateUser({ ...user, ...identityUpdate } as User);
    }

    return { message: `updated user ${user.id}` };
  }

  private async initiateVerificationEmail({
    email,
    name,
    tenantId,
    partnerId,
  }: { email: string; name: string; tenantId: string; partnerId?: string }) {
    const token = await this.admin.createToken({ email, partnerId, tenantId });
    await this.notification.sendEmail({
      recipients: [{ name, email, variables: { token, tokenLifetime: '1' } }],
      // TODO update this template to allow for optional partner/whitelabeling?
      notificationConfig: {
        type: NotificationType.AuthenticationIdentityVerifyEmail,
        context: { partnerId },
        variables: { TOKEN: '%recipient.token%', TOKEN_LIFETIME: '%recipient.tokenLifetime%' },
      },
    });
  }

  async sendResetPasswordEmail({ tenantId, email, partnerId }: ResetPasswordRequest) {
    const user = await this.admin.getUserByEmail(email, tenantId);

    if (!user)
      throw new RpcException({
        code: status.NOT_FOUND,
        message: `user not found with email ${email} in tenant ${tenantId}`,
      });

    const token = await this.admin.createToken({ email, partnerId, tenantId });
    await this.notification.sendEmail({
      recipients: [{ name: user?.displayName, email, variables: { token, email } }],
      notificationConfig: {
        type: NotificationType.AuthenticationResetPassword,
        context: { partnerId },
        variables: { TOKEN: '%recipient.token%', EMAIL: '%recipient.email%' },
      },
    });
    return { message: 'reset password email sent' };
  }

  async sendVerificationEmail({
    email: rawEmail,
    tenantId,
    partnerId,
  }: SendVerificationEmailRequest) {
    const email = rawEmail.toLowerCase();
    const user = await this.identityUserRepo.findOne({ where: { email: ILike(email) } });

    if (!user?.email)
      throw new RpcException({
        code: status.NOT_FOUND,
        message: `user not found with email ${email}`,
      });

    await this.initiateVerificationEmail({
      tenantId,
      email: user.email,
      name: user?.name || user.email,
      partnerId,
    });
    return { message: 'verification email sent' };
  }

  async readPortalRoles(request: ReadPortalRolesRequest): Promise<ReadPortalRolesResponse> {
    this.logger.debug({ request }, 'readPortalRoles request');
    let userId = '';
    if (request.userIdentifier === 'identityUserId') userId = request.identityUserId;
    if (request.userIdentifier === 'coreUserId') {
      const user = await this.findUserByCoreId(request.coreUserId, 'advocate');
      userId = user?.id;
    }
    if (!userId) {
      throw new RpcException({
        status: Status.NOT_FOUND,
        message: `user not found with core user id ${request.coreUserId}`,
      });
    }
    const roles = await this.authZ.getPartnerPortalRoles(userId, request.partnerId);
    return {
      message: `roles retrieved successfully for user:${userId} and partner:${request.partnerId}`,
      roles,
    };
  }
}
