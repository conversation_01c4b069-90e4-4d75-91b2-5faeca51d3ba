import { FeatureName } from '@bybeam/platform-types';
import { ValidationType } from '@bybeam/verification-client';
import { Test, TestingModule } from '@nestjs/testing';
import { PinoLogger } from 'nestjs-pino';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { VerificationService } from '../verification/verification.service.js';
import { ValidationsService } from './validations.service.js';

describe('ValidationsService', () => {
  let service: ValidationsService;
  let logger: PinoLogger;

  const mockValidateUser = vi.fn();
  const mockValidate = vi.fn();
  const mockFindPartner = vi.fn();
  const mockLogger = {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ValidationsService],
    })
      .useMocker((token) => {
        if (token === 'PinoLogger:ValidationsService') return mockLogger;
        if (token === VerificationService) {
          return {
            validateUser: mockValidateUser,
            validate: mockValidate,
          };
        }
        if (token === 'IDENTITY_UserEntityRepository') {
          return {
            createQueryBuilder: vi.fn().mockReturnValue({
              leftJoinAndSelect: vi.fn().mockReturnThis(),
              where: vi.fn().mockReturnThis(),
              orWhere: vi.fn().mockReturnThis(),
              limit: vi.fn().mockReturnThis(),
              offset: vi.fn().mockReturnThis(),
              getCount: vi.fn().mockResolvedValue(2),
              getMany: vi.fn().mockResolvedValue([
                { id: 'userA', phone: '+**********', email: '<EMAIL>' },
                { id: 'userB', phone: '+**********', email: '<EMAIL>' },
              ]),
            }),
          };
        }
        if (token === 'IDENTITY_SessionEntityRepository') {
          return {
            createQueryBuilder: vi.fn().mockReturnValue({
              addCommonTableExpression: vi.fn().mockReturnThis(),
              innerJoin: vi.fn().mockReturnThis(),
              where: vi.fn().mockReturnThis(),
              andWhere: vi.fn().mockReturnThis(),
              limit: vi.fn().mockReturnThis(),
              offset: vi.fn().mockReturnThis(),
              getCount: vi.fn().mockResolvedValue(3),
              setParameters: vi.fn().mockReturnThis(),
              getMany: vi.fn().mockResolvedValue([
                {
                  id: 'session1',
                  userId: 'userA',
                  headers: { 'cf-connecting-ip': '***********' },
                },
                {
                  id: 'session2',
                  userId: 'userB',
                  headers: { 'cf-connecting-ip': '***********' },
                },
                {
                  id: 'session3',
                  userId: 'user3',
                  headers: { 'cf-connecting-ip': '***********' },
                },
              ]),
            }),
          };
        }
        if (token === 'CORE_PartnerEntityRepository') {
          return {
            findOne: mockFindPartner.mockResolvedValue({
              id: 'test-partner-id',
              externalId: 'test-external-id',
              features: [{ enabled: true, feature: { name: FeatureName.UserValidation } }],
            }),
          };
        }
      })
      .compile();

    service = module.get<ValidationsService>(ValidationsService);
    logger = module.get<PinoLogger>('PinoLogger:ValidationsService');
  });

  describe('migrateUserProfileValidations', () => {
    it('should migrate validations for user profiles', async () => {
      const partnerId = 'test-partner-id';
      const result = await service.migrateUserProfileValidations({ partnerId });

      expect(result).toEqual({
        message: `successfully migrated validations for ${partnerId}`,
      });
    });

    it('should call validateUser for each user with correct parameters', async () => {
      const partnerId = 'test-partner-id';
      await service.migrateUserProfileValidations({ partnerId });

      expect(mockValidateUser).toHaveBeenCalledTimes(2);
      expect(mockValidateUser).toHaveBeenCalledWith({
        id: 'userA',
        phone: '+**********',
        email: '<EMAIL>',
      });
      expect(mockValidateUser).toHaveBeenCalledWith({
        id: 'userB',
        phone: '+**********',
        email: '<EMAIL>',
      });
    });

    it('should log successful migration for each batch', async () => {
      const partnerId = 'test-partner-id';
      await service.migrateUserProfileValidations({ partnerId });

      expect(logger.info).toHaveBeenCalledWith(
        { partnerId, userIds: ['userA', 'userB'] },
        'Successfully migrated validations for 2 users',
      );
      expect(logger.info).toHaveBeenCalledWith(
        { partnerId },
        'Successfully migrated validations to existing users',
      );
    });

    it('should throw error when user validation feature `User: Validation` is disabled', async () => {
      const partnerId = 'test-partner-id';
      mockFindPartner.mockResolvedValueOnce({
        id: 'test-partner-id',
        externalId: 'test-external-id',
        features: [{ enabled: false, feature: { name: FeatureName.UserValidation } }],
      });
      await expect(service.migrateUserProfileValidations({ partnerId })).rejects.toThrow(
        'user validation is not enabled for test-partner-id',
      );

      expect(logger.info).toHaveBeenCalledWith(
        { partnerId },
        'User validation is not enabled for partner',
      );
      expect(mockValidateUser).not.toHaveBeenCalled();
    });
  });

  describe('migrateUserSessionValidations', () => {
    it('should migrate validations for user sessions', async () => {
      const partnerId = 'test-partner-id';
      const result = await service.migrateUserSessionValidations({ partnerId });

      expect(result).toEqual({
        message: `successfully migrated validations for ${partnerId}`,
      });
    });

    it('should call validate for each session with correct parameters', async () => {
      const partnerId = 'test-partner-id';
      await service.migrateUserSessionValidations({ partnerId });

      expect(mockValidate).toHaveBeenCalledTimes(3);
      expect(mockValidate).toHaveBeenCalledWith({
        userId: 'userA',
        partnerId,
        value: '***********',
        type: ValidationType.IPAddress,
      });
      expect(mockValidate).toHaveBeenCalledWith({
        userId: 'userB',
        partnerId,
        value: '***********',
        type: ValidationType.IPAddress,
      });
      expect(mockValidate).toHaveBeenCalledWith({
        userId: 'user3',
        partnerId,
        value: '***********',
        type: ValidationType.IPAddress,
      });
    });
    it('should throw error when user validation feature `User: Validation` is disabled', async () => {
      mockFindPartner.mockResolvedValueOnce({
        id: 'test-partner-id',
        externalId: 'test-external-id',
        features: [{ enabled: false, feature: { name: FeatureName.UserValidation } }],
      });
      const partnerId = 'test-partner-id';

      await expect(service.migrateUserSessionValidations({ partnerId })).rejects.toThrow(
        'user validation is not enabled for test-partner-id',
      );

      expect(logger.info).toHaveBeenCalledWith(
        { partnerId },
        'User validation is not enabled for partner',
      );
      expect(mockValidate).not.toHaveBeenCalled();
    });
  });
});
