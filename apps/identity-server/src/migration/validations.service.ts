import { MigrateValidationsRequest } from '@bybeam/identity-client/types';
import { SessionEntity, UserEntity } from '@bybeam/identity-entities';
import { queryAndBatchAction } from '@bybeam/infrastructure-lib/batch';
import { PartnerEntity } from '@bybeam/platform-entities';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import { FeatureName } from '@bybeam/platform-types';
import { ValidationType } from '@bybeam/verification-client';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import { Repository } from 'typeorm';
import { Database } from '../database/database.js';
import { VerificationService } from '../verification/verification.service.js';

@Injectable()
export class ValidationsService {
  constructor(
    @InjectPinoLogger(ValidationsService.name) private readonly logger: PinoLogger,
    private readonly verificationService: VerificationService,
    @InjectRepository(UserEntity, Database.Identity)
    private userRepo: Repository<UserEntity>,
    @InjectRepository(SessionEntity, Database.Identity)
    private sessionRepo: Repository<SessionEntity>,
    @InjectRepository(PartnerEntity, Database.Core)
    private partnerRepo: Repository<PartnerEntity>,
  ) {}

  private async isUserValidationEnabled(partnerId: string) {
    const partner = await this.partnerRepo.findOne({
      where: { id: partnerId },
      relations: ['features'],
    });
    return checkFeature(partner?.features, FeatureName.UserValidation);
  }

  async migrateUserProfileValidations({ partnerId }: MigrateValidationsRequest) {
    const isEnabled = await this.isUserValidationEnabled(partnerId);
    if (!isEnabled) {
      this.logger.info({ partnerId }, 'User validation is not enabled for partner');
      throw new Error(`user validation is not enabled for ${partnerId}`);
    }

    const query = this.userRepo
      .createQueryBuilder('users')
      .leftJoinAndSelect('users.advocates', 'advocate')
      .leftJoinAndSelect('users.applicants', 'applicant')
      .where('applicant.partnerId = :partnerId', { partnerId })
      .orWhere('advocate.partnerId = :partnerId', { partnerId });

    await queryAndBatchAction({
      query: async (skip, take) => {
        this.logger.info(`query skip: ${skip}, take: ${take}`);
        return query.limit(take).offset(skip).getMany();
      },
      action: async (users) => {
        await Promise.all(
          users.map(async (user) =>
            this.verificationService.validateUser({
              id: user.id,
              phone: user.phone,
              email: user.email,
            }),
          ),
        );
        this.logger.info(
          { partnerId, userIds: users?.map((user) => user.id) },
          `Successfully migrated validations for ${users.length} users`,
        );
      },
      batchSize: 10,
      totalCount: await query.getCount(),
      delay: 1000,
    });

    this.logger.info({ partnerId }, 'Successfully migrated validations to existing users');
    return { message: `successfully migrated validations for ${partnerId}` };
  }

  async migrateUserSessionValidations({ partnerId }: MigrateValidationsRequest) {
    const isEnabled = await this.isUserValidationEnabled(partnerId);
    if (!isEnabled) {
      this.logger.info({ partnerId }, 'User validation is not enabled for partner');
      throw new Error(`user validation is not enabled for ${partnerId}`);
    }

    const query = this.sessionRepo
      .createQueryBuilder('sessions')
      .addCommonTableExpression(
        `SELECT DISTINCT user_id,
                FIRST_VALUE(id) OVER (PARTITION BY user_id ORDER BY started_at DESC) latest_id
           FROM sessions
          WHERE claims->>'partnerId' = :partnerId
            AND headers->>'cf-connecting-ip' IS NOT NULL`,
        'latest',
      )
      .setParameters({ partnerId })
      .innerJoin('latest', 'latest', 'sessions.id = latest.latest_id')
      // This is not needed, but it's a good practice to filter out sessions that don't have an IP address
      .where(`claims->>'partnerId' = :partnerId`, { partnerId })
      .andWhere(`headers->>'cf-connecting-ip' IS NOT NULL`);

    const totalCount = await query.getCount();

    await queryAndBatchAction({
      query: async (skip, take) => {
        this.logger.info(`query skip: ${skip}, take: ${take}`);
        return query.limit(take).offset(skip).getMany();
      },
      action: async (sessions) => {
        await Promise.all(
          sessions.map(async (session) => {
            return this.verificationService.validate({
              userId: session.userId,
              partnerId,
              value: session?.headers?.['cf-connecting-ip'] ?? '',
              type: ValidationType.IPAddress,
            });
          }),
        );
        this.logger.info(
          { partnerId, sessionIds: sessions?.map((sessions) => sessions.id) },
          `Successfully migrated validations for ${sessions.length} sessions`,
        );
      },
      batchSize: 10,
      totalCount: totalCount,
      delay: 1000,
    });

    this.logger.info(
      { partnerId, totalCount },
      `Successfully migrated validations for ${totalCount} sessions`,
    );
    return { message: `successfully migrated validations for ${partnerId}` };
  }
}
