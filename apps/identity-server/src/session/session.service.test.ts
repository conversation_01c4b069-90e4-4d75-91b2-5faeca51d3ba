import { FeatureName } from '@bybeam/platform-types';
import { Test, TestingModule } from '@nestjs/testing';
import dayjs from 'dayjs';
import { describe, expect, it, vi } from 'vitest';
import { AdminService } from '../admin/admin.service.js';
import { PermissionsService } from '../user/permissions.service.js';
import { UserService } from '../user/user.service.js';
import { VerificationService } from '../verification/verification.service.js';
import { SessionService } from './session.service.js';

const mockLogger = { debug: vi.fn(), info: vi.fn(), warn: vi.fn(), error: vi.fn() };

const date = new Date('2020-10-20');
vi.useFakeTimers();
vi.setSystemTime(date);

vi.mock('node:crypto', async (importOriginal) => ({
  ...importOriginal(),
  randomUUID: vi.fn().mockReturnValue('mockRandomUUID'),
}));

describe('session', () => {
  describe('beginSession', () => {
    it('verifies the user token and sets applicant claims', async () => {
      const setUserClaimFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [SessionService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:SessionService') return mockLogger;
          if (token === AdminService)
            return {
              setUserClaims: setUserClaimFn.mockResolvedValueOnce('success'),
            };
          if (token === VerificationService) return { validate: vi.fn() };
          if (token === 'IDENTITY_SessionEntityRepository')
            return { insert: vi.fn(), update: vi.fn() };
          if (token === PermissionsService)
            return {
              getClaims: vi.fn().mockReturnValueOnce({
                gcipUid: 'mockGCIPUID',
                tenantId: 'app-mock-123',
                userId: 'mockCoreUserId',
                partnerId: 'mockPartnerId',
                sessionId: 'mockRandomUUID',
              }),
            };
          if (token === UserService)
            return {
              validateByToken: vi.fn().mockResolvedValueOnce({
                id: 'mockUserId',
                gcipUid: 'mockGCIPUID',
                applicants: [{ id: 'mockCoreUserId', partnerId: 'mockPartnerId' }],
                advocates: [],
              }),
            };
          if (token === 'CORE_PartnerEntityRepository')
            return {
              findOne: vi.fn().mockResolvedValue({
                id: 'mockPartnerId',
                externalId: 'mockExternalId',
                features: [{ enabled: true, feature: { name: FeatureName.UserValidation } }],
              }),
            };
        })
        .compile();

      const result = await module.get<SessionService>(SessionService).beginSession({
        userId: '',
        _userId: 'userId',
        partnerId: 'mockPartnerId',
        token: 'mockToken',
        tenantId: 'app-mock-123',
        authenticationMechanism: 'UNKNOWN',
        headers: JSON.stringify({ test: 'something ' }),
      });
      expect(result.message).toBe('session begun');
      expect(setUserClaimFn).toHaveBeenCalledWith({
        gcipUid: 'mockGCIPUID',
        tenantId: 'app-mock-123',
        partnerId: 'mockPartnerId',
        userId: 'mockCoreUserId',
        sessionId: 'mockRandomUUID',
      });
    });
    it('saves a row to the sessions table with the headers and claims, then updates it with the found user id', async () => {
      const setUserClaimFn = vi.fn();
      const insertSessionFn = vi.fn();
      const updateSessionFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [SessionService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:SessionService') return mockLogger;
          if (token === AdminService)
            return {
              setUserClaims: setUserClaimFn.mockResolvedValueOnce('success'),
            };
          if (token === VerificationService) return { validate: vi.fn() };
          if (token === 'IDENTITY_SessionEntityRepository')
            return { insert: insertSessionFn, update: updateSessionFn };
          if (token === PermissionsService)
            return {
              getClaims: vi.fn().mockReturnValueOnce({
                gcipUid: 'mockGCIPUID',
                tenantId: 'app-mock-123',
                userId: 'mockCoreUserId',
                partnerId: 'mockPartnerId',
                sessionId: 'mockRandomUUID',
              }),
            };
          if (token === UserService)
            return {
              validateByToken: vi.fn().mockResolvedValueOnce({
                id: 'mockUserId',
                gcipUid: 'mockGCIPUID',
                applicants: [{ id: 'mockCoreUserId', partnerId: 'mockPartnerId' }],
                advocates: [],
              }),
            };
          if (token === 'CORE_PartnerEntityRepository')
            return {
              findOne: vi.fn().mockResolvedValue({
                id: 'mockPartnerId',
                externalId: 'mockExternalId',
                features: [{ enabled: true, feature: { name: FeatureName.UserValidation } }],
              }),
            };
        })
        .compile();

      const result = await module.get<SessionService>(SessionService).beginSession({
        userId: '',
        _userId: 'userId',
        partnerId: 'mockPartnerId',
        token: 'mockToken',
        tenantId: 'app-mock-123',
        authenticationMechanism: 'UNKNOWN',
        headers: JSON.stringify({ test: 'something', 'x-gcip-token': 'this should be ignored' }),
      });
      expect(result.message).toBe('session begun');
      expect(setUserClaimFn).toHaveBeenCalledWith({
        gcipUid: 'mockGCIPUID',
        tenantId: 'app-mock-123',
        partnerId: 'mockPartnerId',
        userId: 'mockCoreUserId',
        sessionId: 'mockRandomUUID',
      });
      expect(insertSessionFn).toHaveBeenCalledWith({
        id: 'mockRandomUUID',
        authenticationMechanism: 'UNKNOWN',
        headers: { test: 'something' },
      });
      expect(updateSessionFn).toHaveBeenCalledWith(
        {
          id: 'mockRandomUUID',
        },
        {
          claims: {
            gcipUid: 'mockGCIPUID',
            partnerId: 'mockPartnerId',
            sessionId: 'mockRandomUUID',
            tenantId: 'app-mock-123',
            userId: 'mockCoreUserId',
          },
          userId: 'mockUserId',
        },
      );
    });
    it('should validate the IP address if `cf-connecting-ip` exists in the headers and feature `User: Validation` is enabled', async () => {
      const validateFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [SessionService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:SessionService') return mockLogger;
          if (token === AdminService)
            return {
              setUserClaims: vi.fn().mockResolvedValueOnce('success'),
            };
          if (token === VerificationService) return { validate: validateFn };
          if (token === 'IDENTITY_SessionEntityRepository')
            return { insert: vi.fn(), update: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository')
            return {
              findOne: vi.fn().mockResolvedValue({
                id: 'mockPartnerId',
                externalId: 'mockExternalId',
                features: [{ enabled: true, feature: { name: FeatureName.UserValidation } }],
              }),
            };
          if (token === PermissionsService)
            return {
              getClaims: vi.fn().mockReturnValueOnce({
                gcipUid: 'mockGCIPUID',
                tenantId: 'app-mock-123',
                userId: 'mockCoreUserId',
                partnerId: 'mockPartnerId',
                sessionId: 'mockRandomUUID',
              }),
            };
          if (token === UserService)
            return {
              validateByToken: vi.fn().mockResolvedValueOnce({
                id: 'mockUserId',
                gcipUid: 'mockGCIPUID',
                applicants: [{ id: 'mockCoreUserId', partnerId: 'mockPartnerId' }],
                advocates: [],
              }),
            };
          if (token === 'CORE_PartnerEntityRepository')
            return {
              findOne: vi.fn().mockResolvedValue({
                id: 'mockPartnerId',
                externalId: 'mockExternalId',
                features: [{ enabled: true, feature: { name: FeatureName.UserValidation } }],
              }),
            };
        })
        .compile();

      const result = await module.get<SessionService>(SessionService).beginSession({
        userId: '',
        _userId: 'userId',
        partnerId: 'mockPartnerId',
        token: 'mockToken',
        tenantId: 'app-mock-123',
        authenticationMechanism: 'UNKNOWN',
        headers: JSON.stringify({ test: 'something ', 'cf-connecting-ip': 'mockIPAddress' }),
      });
      expect(result.message).toBe('session begun');
      expect(validateFn).toHaveBeenCalledWith({
        partnerId: 'mockPartnerId',
        userId: 'mockUserId',
        value: 'mockIPAddress',
        type: 'IPAddress',
      });
    });
    it('throws UNAUTHENTICATED if there is an error', async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [SessionService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:SessionService') return mockLogger;
          if (token === AdminService)
            return {
              setUserClaims: vi.fn().mockResolvedValueOnce('success'),
            };
          if (token === VerificationService) return { validate: vi.fn() };
          if (token === 'IDENTITY_SessionEntityRepository') return { save: vi.fn() };
          if (token === PermissionsService) return {};
          if (token === UserService)
            return {
              validateByToken: vi.fn().mockRejectedValueOnce('big issues for you'),
            };
          if (token === 'CORE_PartnerEntityRepository')
            return {
              findOne: vi.fn().mockResolvedValue({
                id: 'mockPartnerId',
                externalId: 'mockExternalId',
                features: [{ enabled: true, feature: { name: FeatureName.UserValidation } }],
              }),
            };
        })
        .compile();
      await expect(
        module.get<SessionService>(SessionService).beginSession({
          userId: '',
          _userId: 'userId',
          partnerId: 'mockPartnerId',
          token: 'mockToken',
          tenantId: 'mockTenantId',
          authenticationMechanism: 'UNKNOWN',
          headers: JSON.stringify({ test: 'something ' }),
        }),
      ).rejects.toThrowError('cannot start session');
    });
    it('should not validate the IP address when feature `User: Validation` is not enabled', async () => {
      const validateFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [SessionService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:SessionService') return mockLogger;
          if (token === AdminService)
            return {
              setUserClaims: vi.fn().mockResolvedValueOnce('success'),
            };
          if (token === VerificationService) return { validate: validateFn };
          if (token === 'IDENTITY_SessionEntityRepository')
            return { insert: vi.fn(), update: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository')
            return {
              findOne: vi.fn().mockResolvedValue({
                id: 'mockPartnerId',
                externalId: 'mockExternalId',
                features: [{ enabled: false, feature: { name: FeatureName.UserValidation } }],
              }),
            };
          if (token === PermissionsService)
            return {
              getClaims: vi.fn().mockReturnValueOnce({
                gcipUid: 'mockGCIPUID',
                tenantId: 'app-mock-123',
                userId: 'mockCoreUserId',
                partnerId: 'mockPartnerId',
                sessionId: 'mockRandomUUID',
              }),
            };
          if (token === UserService)
            return {
              validateByToken: vi.fn().mockResolvedValueOnce({
                id: 'mockUserId',
                gcipUid: 'mockGCIPUID',
                applicants: [{ id: 'mockCoreUserId', partnerId: 'mockPartnerId' }],
                advocates: [],
              }),
            };
        })
        .compile();

      const result = await module.get<SessionService>(SessionService).beginSession({
        userId: '',
        _userId: 'userId',
        partnerId: 'mockPartnerId',
        token: 'mockToken',
        tenantId: 'app-mock-123',
        authenticationMechanism: 'UNKNOWN',
        headers: JSON.stringify({ test: 'something ', 'cf-connecting-ip': 'mockIPAddress' }),
      });
      expect(result.message).toBe('session begun');
      expect(validateFn).not.toHaveBeenCalled();
    });
  });
  describe('endSession', () => {
    it('should end the session in firebase', async () => {
      const saveSessionFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [SessionService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:SessionService') return mockLogger;
          if (token === AdminService)
            return {
              endSession: vi.fn().mockResolvedValueOnce('success'),
              verifyToken: vi.fn().mockResolvedValueOnce(null),
            };
          if (token === VerificationService) return { validate: vi.fn() };
          if (token === 'IDENTITY_SessionEntityRepository')
            return { findOne: vi.fn().mockResolvedValueOnce(null), save: saveSessionFn };
          if (token === PermissionsService) return {};
          if (token === UserService)
            return { findByGCIPUID: vi.fn().mockResolvedValueOnce({ id: 'mockUserId' }) };
          if (token === 'CORE_PartnerEntityRepository') return {};
        })
        .compile();
      const result = await module.get<SessionService>(SessionService).endSession({
        token: 'mockToken',
        tenantId: 'mockTenantId',
      });
      expect(result.message).toBe('session ended');
      expect(saveSessionFn).not.toHaveBeenCalled();
    });
    it('should save the end timestamp in the session table if there is a sessionId in the token', async () => {
      const saveSessionFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [SessionService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:SessionService') return mockLogger;
          if (token === AdminService)
            return {
              verifyToken: vi.fn().mockResolvedValueOnce({ beam: { sessionId: 'mockSessionId' } }),
              endSession: vi.fn().mockResolvedValueOnce('success'),
            };
          if (token === 'IDENTITY_SessionEntityRepository')
            return {
              findOne: vi.fn().mockResolvedValueOnce({ id: 'mockSessionId' }),
              save: saveSessionFn,
            };
          if (token === PermissionsService) return {};
          if (token === UserService)
            return { findByGCIPUID: vi.fn().mockResolvedValueOnce({ id: 'mockUserId' }) };
          if (token === VerificationService) return { validate: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository') return {};
        })
        .compile();
      const result = await module.get<SessionService>(SessionService).endSession({
        token: 'mockToken',
        tenantId: 'mockTenantId',
      });
      expect(result.message).toBe('session ended');
      expect(saveSessionFn).toHaveBeenCalledWith({
        id: 'mockSessionId',
        endedAt: dayjs.utc(),
      });
    });
  });
  describe('validateSession', () => {
    it('verifies the token and returns the claims', async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [SessionService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:SessionService') return mockLogger;
          if (token === AdminService)
            return {
              verifyToken: vi.fn().mockResolvedValueOnce({
                beam: { userId: 'mockUserId', partnerId: 'mockPartnerId' },
              }),
            };
          if (token === 'IDENTITY_SessionEntityRepository') return { save: vi.fn() };
          if (token === PermissionsService) return {};
          if (token === UserService) return {};
          if (token === VerificationService) return { validate: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository') return {};
        })
        .compile();
      const result = await module.get<SessionService>(SessionService).validateSession({
        token: 'mockToken',
        tenantId: 'mockTenantId',
      });
      expect(result.message).toBe('session valid');
      expect(result.claims).toEqual({ userId: 'mockUserId', partnerId: 'mockPartnerId' });
    });
    it('throws UNAUTHENTICATED if there is an error', async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [SessionService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:SessionService') return mockLogger;
          if (token === AdminService)
            return {
              verifyToken: vi.fn().mockRejectedValueOnce(new Error('you did it this time')),
            };
          if (token === 'IDENTITY_SessionEntityRepository') return { save: vi.fn() };
          if (token === PermissionsService) return {};
          if (token === UserService) return {};
          if (token === VerificationService) return { validate: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository') return {};
        })
        .compile();
      await expect(() =>
        module.get<SessionService>(SessionService).validateSession({
          token: 'mockToken',
          tenantId: 'mockTenantId',
        }),
      ).rejects.toThrowError('session invalid');
    });
  });
});
