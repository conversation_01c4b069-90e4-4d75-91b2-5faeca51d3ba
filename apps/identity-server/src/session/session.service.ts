import { randomUUID } from 'node:crypto';
import { SessionRequest, ValidateRequest } from '@bybeam/identity-client/types';
import { SessionEntity } from '@bybeam/identity-entities';
import { PartnerEntity } from '@bybeam/platform-entities';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import { CustomHeaders, FeatureName } from '@bybeam/platform-types';
import { ValidationRequest__Optional } from '@bybeam/verification-client';
import { ValidationType } from '@bybeam/verification-client';
import { status } from '@grpc/grpc-js';
import { Injectable } from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';
import { InjectRepository } from '@nestjs/typeorm';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import { Repository } from 'typeorm';
import { AdminService } from '../admin/admin.service.js';
import { Database } from '../database/database.js';
import { PermissionsService } from '../user/permissions.service.js';
import { UserService } from '../user/user.service.js';
import dayjs from '../utils/dayjs.js';
import { isFirebaseError } from '../utils/errors.js';
import { VerificationService } from '../verification/verification.service.js';

@Injectable()
export class SessionService {
  constructor(
    private adminService: AdminService,
    private permissionsService: PermissionsService,
    private userService: UserService,
    private verificationService: VerificationService,
    @InjectPinoLogger(SessionService.name) private readonly logger: PinoLogger,
    @InjectRepository(SessionEntity, Database.Identity)
    private sessionRepo: Repository<SessionEntity>,
    @InjectRepository(PartnerEntity, Database.Core)
    private partnerRepo: Repository<PartnerEntity>,
  ) {}

  private sanitizeHeaders(headers: { [key: string]: string }) {
    const IGNORE_LIST = Object.values(CustomHeaders);
    return Object.fromEntries(
      Object.entries(headers)
        .map(([key, value]) => {
          if (IGNORE_LIST.includes(key as CustomHeaders)) return;
          return [key, value];
        })
        .filter(Boolean),
    );
  }

  private async validateIpAddress(request: ValidationRequest__Optional): Promise<void> {
    const partner = await this.partnerRepo.findOne({
      where: { id: request.partnerId },
      relations: ['features'],
    });
    if (checkFeature(partner?.features, FeatureName.UserValidation)) {
      await this.verificationService.validate(request);
    }
  }

  async beginSession(input: SessionRequest) {
    try {
      this.logger.debug({ input }, 'beginSession request');
      const { partnerId, tenantId, authenticationMechanism } = input;
      const sessionId = randomUUID();
      const sanitizeHeaders = this.sanitizeHeaders(JSON.parse(input.headers));
      await this.sessionRepo.insert({
        id: sessionId,
        authenticationMechanism,
        headers: sanitizeHeaders,
      });
      const user = await this.userService.validateByToken({ ...input, sessionId });
      const claims = await this.permissionsService.getClaims({
        user,
        partnerId,
        sessionId,
        tenantId,
      });
      await this.sessionRepo.update({ id: sessionId }, { userId: user.id, claims });
      this.logger.debug({ claims }, 'claims');
      await this.adminService.setUserClaims(claims);

      if (sanitizeHeaders['cf-connecting-ip']) {
        this.validateIpAddress({
          userId: user.id,
          partnerId,
          value: sanitizeHeaders['cf-connecting-ip'],
          type: ValidationType.IPAddress,
        });
      }
      return { message: 'session begun' };
    } catch (error) {
      if (error instanceof RpcException) {
        this.logger.warn(error, 'known session error');
        throw error;
      }
      this.logger.error(error, 'unexpected error');
      throw new RpcException({ code: status.UNAUTHENTICATED, message: 'cannot start session' });
    }
  }

  async endSession(input: ValidateRequest) {
    this.logger.debug({ input }, 'endSession request');
    const decodedToken = await this.adminService.verifyToken(input.token, input.tenantId);
    await this.adminService.endSession(input);
    if (decodedToken?.beam?.sessionId)
      await this.sessionRepo.save({ id: decodedToken.beam.sessionId, endedAt: dayjs.utc() });
    return { message: 'session ended' };
  }

  async validateSession(input: ValidateRequest) {
    this.logger.debug({ input }, 'validateSession request');
    try {
      const claims = await this.adminService.verifyToken(input.token, input.tenantId);
      return { message: 'session valid', claims: claims.beam };
    } catch (error) {
      this.handleKnownErrors(error);
      throw new RpcException({ code: status.UNAUTHENTICATED, message: 'session invalid' });
    }
  }

  private handleKnownErrors(error: unknown) {
    if (isFirebaseError(error)) {
      switch (error.code) {
        case 'auth/id-token-expired':
        case 'auth/id-token-revoked':
          this.logger.warn({ error }, 'known warning');
          break;
        default:
          this.logger.error({ error }, 'unexpected error');
          break;
      }
    }
  }
}
