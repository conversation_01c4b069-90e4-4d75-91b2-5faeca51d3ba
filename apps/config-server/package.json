{"name": "@bybeam/config-server", "version": "0.0.0", "private": true, "type": "module", "scripts": {"build": "nest build", "nest": "nest", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "test": "vitest"}, "devDependencies": {"@bybeam/platform-types": "workspace:^", "@nestjs/cli": "^10.3.2", "@nestjs/schematics": "^10.0.3", "@nestjs/testing": "^10.3.0", "@tsconfig/node20": "20.1.4", "@types/node": "22.0.0", "@vitest/coverage-v8": "2.1.9", "source-map-support": "0.5.21", "ts-loader": "^9.5.1", "ts-node": "10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "5.5.4", "unplugin-swc": "^1.4.5", "vitest": "2.1.9"}, "dependencies": {"@bybeam/config-client": "workspace:*", "@bybeam/infrastructure-lib": "workspace:*", "@grpc/grpc-js": "1.10.10", "@grpc/proto-loader": "0.7.10", "@nestjs/common": "^11.0.12", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.3.5", "@nestjs/microservices": "^10.4.6", "@nestjs/terminus": "^11.0.0", "@nestjs/typeorm": "^11.0.0", "grpc-health-check": "^2.0.0", "nestjs-grpc-reflection": "^0.2.2", "nestjs-pino": "4.0.0", "pg": "8.13.1", "reflect-metadata": "0.1.14", "rxjs": "^7.8.1", "typeorm": "0.3.17"}}