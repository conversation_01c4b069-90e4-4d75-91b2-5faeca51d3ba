import {
  CreateTagAutomationInput,
  Criteria,
  CriteriaOperator,
  CriteriaType,
  DeleteTagAutomationInput,
  MutationResponse,
  TagAutomation,
  TagAutomationCriteria,
  TagAutomationTriggerType,
  UpdateTagAutomationInput,
  WorkflowAction,
} from '@bybeam/platform-types';
import { TagAutomationRepository } from '@platform-api/@types/repositories/core.js';
import QueryServiceImplementation from '../QueryService.js';
import {
  ApplicationScoreService,
  CaseTagService,
  TagAutomationsService as ITagAutomationsService,
  WorkflowEventService,
} from '../../@types/services.js';
import { AdminToken } from '@platform-api/@types/authentication.js';
import * as response from '../../utils/response.js';
import { ServiceError, status } from '@grpc/grpc-js';
import { StatusCodes } from 'http-status-codes';
import { logger } from '../../utils/logger.js';
import { ApplicationSubmissionEvent } from '@platform-api/@types/events.js';
import getTierFromScore from '@bybeam/platform-lib/application/getTierFromScore';

export default class TagAutomationsService
  extends QueryServiceImplementation<TagAutomation, TagAutomationRepository>
  implements ITagAutomationsService
{
  private workflowEventService: WorkflowEventService;
  private caseTagService: CaseTagService;
  private applicationScoreService: ApplicationScoreService;
  constructor({
    repository,
    workflowEventService,
    caseTagService,
    applicationScoreService,
  }: {
    repository: TagAutomationRepository;
    workflowEventService: WorkflowEventService;
    caseTagService: CaseTagService;
    applicationScoreService: ApplicationScoreService;
  }) {
    super(repository);
    this.workflowEventService = workflowEventService;
    this.caseTagService = caseTagService;
    this.applicationScoreService = applicationScoreService;
  }
  public async findByPartnerId(partnerId: string): Promise<TagAutomation[]> {
    return this.repository.find({
      where: { partnerId },
      relations: ['tags', 'program', 'partner'],
    });
  }
  public async findByProgramId(programId: string): Promise<TagAutomation[]> {
    return this.repository.find({
      where: { programId },
      relations: ['tags', 'program', 'partner'],
    });
  }
  public async findByTriggerType({
    partnerId,
    triggerType,
  }: {
    triggerType: TagAutomationTriggerType;
    partnerId: string;
  }): Promise<TagAutomation[]> {
    return this.repository.findBy({ triggerType, partnerId });
  }
  public async create(
    token: AdminToken,
    input: CreateTagAutomationInput,
  ): Promise<MutationResponse<TagAutomation>> {
    try {
      const tagAutomation = await this.repository.save({
        ...input,
        tags: input.tagIds.map((tagId) => ({ id: tagId })),
        partnerId: token.partnerId,
      });
      const record = await this.repository.findOne({
        where: { id: tagAutomation.id },
        relations: ['tags', 'program', 'partner'],
      });
      if (!record) {
        throw new Error('Tag automation not created.');
      }
      await this.workflowEventService.create(token, {
        entityId: tagAutomation.id,
        entityType: 'tag_automation',
        action: WorkflowAction.CreateTagAutomation,
      });
      return response.success({ record });
    } catch (err) {
      logger.error({ err }, `${this.constructor.name}.create: unexpected error for >`);
      return response.error(this.getErrorDetails(err as ServiceError));
    }
  }
  public async update(
    token: AdminToken,
    input: UpdateTagAutomationInput,
  ): Promise<MutationResponse<TagAutomation>> {
    try {
      const { deactivate, ...rest } = input;
      const isActive = !input.deactivate;
      const updatedRecord = await this.repository.preload({
        ...rest,
        id: input.id,
        programId: input.programId ?? null,
        tags: input.tagIds?.map((tagId) => ({ id: tagId })),
        deactivatedAt: isActive ? undefined : new Date(),
        partnerId: token.partnerId,
      });
      if (!updatedRecord) {
        throw new Error('Tag automation could not updated.');
      }

      await this.repository.save(updatedRecord);
      const record = (await this.repository.findOne({
        where: { id: input.id },
        relations: ['tags', 'program', 'partner'],
      })) as TagAutomation;
      await this.workflowEventService.create(token, {
        entityId: record.id,
        entityType: 'tag_automation',
        action: WorkflowAction.UpdateTagAutomation,
      });
      return response.success({
        record,
      });
    } catch (err) {
      logger.error({ err }, `${this.constructor.name}.update: unexpected error for >`);
      return response.error(this.getErrorDetails(err as ServiceError));
    }
  }
  public async delete(
    token: AdminToken,
    input: DeleteTagAutomationInput,
  ): Promise<MutationResponse<TagAutomation>> {
    try {
      await this.repository.softDelete(input.id);
      await this.workflowEventService.create(token, {
        entityId: input.id,
        entityType: 'tag_automation',
        action: WorkflowAction.DeleteTagAutomation,
      });
      return response.success();
    } catch (err) {
      logger.error({ err }, `${this.constructor.name}.delete: unexpected error for >`);
      return response.error(this.getErrorDetails(err as ServiceError));
    }
  }

  public async process(input: ApplicationSubmissionEvent): Promise<void> {
    logger.info(`${this.constructor.name}.process: processing tag automations for ${input.caseId}`);
    try {
      const automations = await this.findByTriggerType({
        partnerId: input.submitter.partnerId,
        triggerType: TagAutomationTriggerType.ApplicationSubmitted,
      });
      if (automations.length === 0) {
        return;
      }
      const tagsToAdd = [];
      for (const automation of automations) {
        if (automation.programId && automation.programId !== input.programId) {
          continue;
        }
        if (automation.criteria) {
          const criteriaMet = await this.evaluateCriteria({
            criteria: automation.criteria,
            applicationVersionId: input.applicationVersionId,
          });

          if (!criteriaMet) continue;

          if (automation.tags.length > 1) {
            const randomTag = automation.tags[Math.floor(Math.random() * automation.tags.length)];
            tagsToAdd.push(randomTag.id);
            continue;
          }
        }
        logger.info(
          `${this.constructor.name}.process: adding tags ${automation.tags.map(
            (tag) => tag.id,
          )} to case ${input.caseId}`,
        );
        tagsToAdd.push(...automation.tags.map((tag) => tag.id));
      }
      Promise.all(
        tagsToAdd.map((tagId) =>
          this.caseTagService.create({
            caseId: input.caseId,
            tagId,
          }),
        ),
      );
    } catch (err) {
      logger.error({ err }, `${this.constructor.name}.process: unexpected error for >`);
    }
  }

  private async evaluateCriteria({
    criteria,
    applicationVersionId,
  }: {
    criteria: TagAutomationCriteria;
    applicationVersionId: string;
  }): Promise<boolean> {
    let result = false;
    const criteriaKeys = Object.keys(criteria) as CriteriaType[];

    for (const key of criteriaKeys) {
      const { op, value } = criteria[key] as Criteria;

      switch (key) {
        case CriteriaType.ApplicationScore: {
          const applicationScore = await this.applicationScoreService.findWithOptions({
            where: { applicationVersionId },
          });
          if (!applicationScore) {
            continue;
          }
          switch (op) {
            case CriteriaOperator.eq: {
              if (getTierFromScore(applicationScore.score).toString() === value) {
                result = true;
              }
              continue;
            }
            case CriteriaOperator.in: {
              if (
                (value as unknown[]).includes(getTierFromScore(applicationScore.score).toString())
              ) {
                result = true;
              }
              continue;
            }
            default: {
              throw new Error(`Unsupported criteria operator: ${op}`);
            }
          }
        }
        default: {
          throw new Error(`Unsupported criteria type: ${key}`);
        }
      }
    }
    return result;
  }

  private getErrorDetails(error: ServiceError): { statusCode: number; errors: string[] } {
    let statusCode: number;

    switch (error.code) {
      case status.INVALID_ARGUMENT:
        statusCode = StatusCodes.BAD_REQUEST;
        break;
      case status.ALREADY_EXISTS:
        statusCode = StatusCodes.BAD_REQUEST;
        break;
      case status.NOT_FOUND:
        statusCode = StatusCodes.NOT_FOUND;
        break;
      default:
        statusCode = StatusCodes.INTERNAL_SERVER_ERROR;
        break;
    }

    return {
      statusCode,
      errors: [error.details || error.message],
    };
  }
}
