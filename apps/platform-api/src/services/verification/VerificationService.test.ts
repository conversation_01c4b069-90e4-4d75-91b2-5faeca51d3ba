import { ApplicationConfig, FieldType } from '@bybeam/platform-types';
import { ServiceType } from '@bybeam/verification-client';
import { mockLogger } from '@platform-api-test/setup/globals.js';
import {
  ApplicationSubmissionEvent,
  EventProducer,
  UploadVerificationFileEvent,
} from '@platform-api/@types/events.js';
import {
  ApplicationVerificationRepository,
  VerificationRepository,
} from '@platform-api/@types/repositories/index.js';
import {
  AddressService,
  ConfigurationService,
  ProgramDocumentService,
  ProgramFeatureService,
} from '@platform-api/@types/services.js';
import VerificationService from './VerificationService.js';

describe('VerificationService', () => {
  describe('verify', () => {
    it('should return early if the program does not have verification features enabled', async () => {
      const mockVerifyFn = vi.fn();
      const mockService = new VerificationService({
        addressService: {} as unknown as AddressService,
        configurationService: {} as unknown as ConfigurationService,
        applicationVerificationRepository: {} as unknown as ApplicationVerificationRepository,
        eventProducer: {} as unknown as EventProducer,
        programDocumentService: {} as unknown as ProgramDocumentService,
        programFeatureService: {
          hasFeatures: vi.fn().mockResolvedValueOnce(false),
        } as unknown as ProgramFeatureService,
        verificationRepository: {
          verify: mockVerifyFn,
        } as unknown as VerificationRepository,
      });
      const response = await mockService.verify({
        applicationVersionId: 'mockVersionId',
        programId: 'mockProgramId',
        submitter: {
          id: 'mockSubmitterId',
          applicantProfile: { applicantTypeId: 'mockApplicantTypeId' },
        },
        answers: { keyA: 'value1' },
      } as unknown as ApplicationSubmissionEvent);
      expect(response).toBeUndefined();
      expect(mockVerifyFn).not.toBeCalled();
    });

    it('should throw if submitter applicant profile not provided', async () => {
      const mockVerifyFn = vi.fn();
      const mockService = new VerificationService({
        addressService: {} as unknown as AddressService,
        configurationService: {} as unknown as ConfigurationService,
        applicationVerificationRepository: {} as unknown as ApplicationVerificationRepository,
        eventProducer: {} as unknown as EventProducer,
        programDocumentService: {} as unknown as ProgramDocumentService,
        programFeatureService: {
          hasFeatures: vi.fn().mockResolvedValueOnce(true),
        } as unknown as ProgramFeatureService,
        verificationRepository: {
          getConfigurations: vi.fn().mockResolvedValueOnce({ configurations: [] }),
          verify: mockVerifyFn,
        } as unknown as VerificationRepository,
      });
      await expect(
        mockService.verify({
          applicationVersionId: 'mockVersionId',
          programId: 'mockProgramId',
          submitter: { id: 'mockSubmitterId' },
          answers: { keyA: 'value1' },
        } as unknown as ApplicationSubmissionEvent),
      ).rejects.toThrow('Please provide applicant profile to verification request');
    });

    it('should return early if the program has no config for that applicant type', async () => {
      const mockVerifyFn = vi.fn();
      const mockService = new VerificationService({
        addressService: {} as unknown as AddressService,
        configurationService: {} as unknown as ConfigurationService,
        applicationVerificationRepository: {} as unknown as ApplicationVerificationRepository,
        eventProducer: {} as unknown as EventProducer,
        programDocumentService: {} as unknown as ProgramDocumentService,
        programFeatureService: {
          hasFeatures: vi.fn().mockResolvedValueOnce(true),
        } as unknown as ProgramFeatureService,
        verificationRepository: {
          getConfigurations: vi.fn().mockResolvedValueOnce({
            configurations: [
              { applicantTypeId: 'otherApplicantType', service: ServiceType.DataLookup },
            ],
          }),
          verify: mockVerifyFn,
        } as unknown as VerificationRepository,
      });
      const response = await mockService.verify({
        applicationVersionId: 'mockVersionId',
        programId: 'mockProgramId',
        submitter: {
          id: 'mockSubmitterId',
          applicantProfile: { applicantTypeId: 'mockApplicantTypeId' },
        },
        answers: { keyA: 'value1' },
      } as unknown as ApplicationSubmissionEvent);
      expect(response).toBeUndefined();
      expect(mockVerifyFn).not.toBeCalled();
    });

    it('should include user fields in the payload', async () => {
      const mockVerifyFn = vi.fn();
      const mockService = new VerificationService({
        addressService: {} as unknown as AddressService,
        configurationService: {
          findApplicationConfigs: vi.fn().mockResolvedValueOnce([
            {
              programId: 'mockProgramId',
              applicantTypeId: 'mockApplicantTypeId',
              configuration: {
                sections: [],
              },
            },
          ]),
        } as unknown as ConfigurationService,
        applicationVerificationRepository: {} as unknown as ApplicationVerificationRepository,
        eventProducer: {} as unknown as EventProducer,
        programDocumentService: {} as unknown as ProgramDocumentService,
        programFeatureService: {
          hasFeatures: vi.fn().mockResolvedValueOnce(true),
        } as unknown as ProgramFeatureService,
        verificationRepository: {
          getConfigurations: vi.fn().mockResolvedValueOnce({
            configurations: [
              {
                programId: 'mockProgramId',
                applicantTypeId: 'mockApplicantTypeId',
                service: ServiceType.DataLookup,
                dataLookup: { fields: [{ key: 'keyA' }, { key: 'name' }, { key: 'email' }] },
              },
            ],
          }),
          verify: mockVerifyFn,
        } as unknown as VerificationRepository,
      });
      await mockService.verify({
        applicationVersionId: 'mockVersionId',
        programId: 'mockProgramId',
        submitter: {
          id: 'mockSubmitterId',
          name: 'Submitter Name',
          email: '<EMAIL>',
          applicantProfile: { applicantTypeId: 'mockApplicantTypeId' },
        },
        answers: { keyA: 'value1' },
      } as unknown as ApplicationSubmissionEvent);
      expect(mockVerifyFn).toBeCalledWith({
        payload: [
          { key: 'keyA', value: 'value1' },
          { key: 'name', value: 'Submitter Name' },
          { key: 'email', value: '<EMAIL>' },
        ],
        applicantTypeId: 'mockApplicantTypeId',
        programId: 'mockProgramId',
        service: 'DataLookup',
      });
    });

    it('should ignore fields that are not in the config when building the payload', async () => {
      const mockVerifyFn = vi.fn();
      const mockService = new VerificationService({
        addressService: {} as unknown as AddressService,
        configurationService: {
          findApplicationConfigs: vi.fn().mockResolvedValueOnce([
            {
              programId: 'mockProgramId',
              applicantTypeId: 'mockApplicantTypeId',
              configuration: {
                sections: [],
              },
            },
          ]),
        } as unknown as ConfigurationService,
        applicationVerificationRepository: {} as unknown as ApplicationVerificationRepository,
        eventProducer: {} as unknown as EventProducer,
        programDocumentService: {} as unknown as ProgramDocumentService,
        programFeatureService: {
          hasFeatures: vi.fn().mockResolvedValueOnce(true),
        } as unknown as ProgramFeatureService,
        verificationRepository: {
          getConfigurations: vi.fn().mockResolvedValueOnce({
            configurations: [
              {
                programId: 'mockProgramId',
                applicantTypeId: 'mockApplicantTypeId',
                service: ServiceType.DataLookup,
                dataLookup: { fields: [{ key: 'keyA' }] },
              },
            ],
          }),
          verify: mockVerifyFn,
        } as unknown as VerificationRepository,
      });
      await mockService.verify({
        applicationVersionId: 'mockVersionId',
        programId: 'mockProgramId',
        submitter: {
          id: 'mockSubmitterId',
          name: 'Submitter Name',
          applicantProfile: { applicantTypeId: 'mockApplicantTypeId' },
        },
        answers: { keyA: 'value1', keyB: 'leaveMeOut' },
      } as unknown as ApplicationSubmissionEvent);
      expect(mockVerifyFn).toBeCalledWith({
        payload: [{ key: 'keyA', value: 'value1' }],
        applicantTypeId: 'mockApplicantTypeId',
        programId: 'mockProgramId',
        service: 'DataLookup',
      });
    });

    it('should ignore fields that are labeled metadata when building the payload', async () => {
      const mockVerifyFn = vi.fn();
      const mockService = new VerificationService({
        addressService: {} as unknown as AddressService,
        configurationService: {
          findApplicationConfigs: vi.fn().mockResolvedValueOnce([
            {
              programId: 'mockProgramId',
              applicantTypeId: 'mockApplicantTypeId',
              configuration: {
                sections: [],
              },
            },
          ]),
        } as unknown as ConfigurationService,
        applicationVerificationRepository: {} as unknown as ApplicationVerificationRepository,
        eventProducer: {} as unknown as EventProducer,
        programDocumentService: {} as unknown as ProgramDocumentService,
        programFeatureService: {
          hasFeatures: vi.fn().mockResolvedValueOnce(true),
        } as unknown as ProgramFeatureService,
        verificationRepository: {
          getConfigurations: vi.fn().mockResolvedValueOnce({
            configurations: [
              {
                programId: 'mockProgramId',
                applicantTypeId: 'mockApplicantTypeId',
                service: ServiceType.DataLookup,
                dataLookup: { fields: [{ key: 'keyA' }, { key: 'keyB', metadata: true }] },
              },
            ],
          }),
          verify: mockVerifyFn,
        } as unknown as VerificationRepository,
      });
      await mockService.verify({
        applicationVersionId: 'mockVersionId',
        programId: 'mockProgramId',
        submitter: {
          id: 'mockSubmitterId',
          name: 'Submitter Name',
          applicantProfile: { applicantTypeId: 'mockApplicantTypeId' },
        },
        answers: { keyA: 'value1', keyB: 'leaveMeOut' },
      } as unknown as ApplicationSubmissionEvent);
      expect(mockVerifyFn).toBeCalledWith({
        payload: [{ key: 'keyA', value: 'value1' }],
        applicantTypeId: 'mockApplicantTypeId',
        programId: 'mockProgramId',
        service: 'DataLookup',
      });
    });

    it('should call the verification client if the features are enabled', async () => {
      const mockVerifyFn = vi.fn();
      const mockService = new VerificationService({
        addressService: {} as unknown as AddressService,
        configurationService: {
          findApplicationConfigs: vi.fn().mockResolvedValueOnce([
            {
              programId: 'mockProgramId',
              applicantTypeId: 'mockApplicantTypeId',
              configuration: {
                sections: [],
              },
            },
          ]),
        } as unknown as ConfigurationService,
        applicationVerificationRepository: {} as unknown as ApplicationVerificationRepository,
        eventProducer: {} as unknown as EventProducer,
        programDocumentService: {} as unknown as ProgramDocumentService,
        programFeatureService: {
          hasFeatures: vi.fn().mockResolvedValueOnce(true),
        } as unknown as ProgramFeatureService,
        verificationRepository: {
          getConfigurations: vi.fn().mockResolvedValueOnce({
            configurations: [
              {
                applicantTypeId: 'mockApplicantTypeId',
                programId: 'mockProgramId',
                service: ServiceType.DataLookup,
                dataLookup: { fields: [{ key: 'keyA' }] },
              },
            ],
          }),
          verify: mockVerifyFn,
        } as unknown as VerificationRepository,
      });
      await mockService.verify({
        applicationVersionId: 'mockVersionId',
        programId: 'mockProgramId',
        submitter: {
          id: 'mockSubmitterId',
          applicantProfile: { applicantTypeId: 'mockApplicantTypeId' },
        },
        answers: { keyA: 'value1' },
      } as unknown as ApplicationSubmissionEvent);
      expect(mockVerifyFn).toBeCalledWith({
        payload: [{ key: 'keyA', value: 'value1' }],
        applicantTypeId: 'mockApplicantTypeId',
        programId: 'mockProgramId',
        service: 'DataLookup',
      });
    });

    it('should log errors in the callback', async () => {
      const mockSaveFn = vi.fn();
      const mockVerifyFn = vi.fn();
      const mockService = new VerificationService({
        addressService: {} as unknown as AddressService,
        configurationService: {
          findApplicationConfigs: vi.fn().mockResolvedValueOnce([
            {
              programId: 'mockProgramId',
              applicantTypeId: 'mockApplicantTypeId',
              configuration: {
                sections: [],
              },
            },
          ]),
        } as unknown as ConfigurationService,
        applicationVerificationRepository: {
          save: mockSaveFn,
        } as unknown as ApplicationVerificationRepository,
        eventProducer: {} as unknown as EventProducer,
        programDocumentService: {} as unknown as ProgramDocumentService,
        programFeatureService: {
          hasFeatures: vi.fn().mockResolvedValueOnce(true),
        } as unknown as ProgramFeatureService,
        verificationRepository: {
          getConfigurations: vi.fn().mockResolvedValueOnce({
            configurations: [
              {
                applicantTypeId: 'mockApplicantTypeId',
                programId: 'mockProgramId',
                service: ServiceType.DataLookup,
                dataLookup: { fields: [{ key: 'keyA' }] },
              },
            ],
          }),
          verify: mockVerifyFn.mockRejectedValueOnce(new Error('callback error')),
        } as unknown as VerificationRepository,
      });
      await mockService.verify({
        applicationVersionId: 'mockVersionId',
        programId: 'mockProgramId',
        submitter: {
          id: 'mockSubmitterId',
          applicantProfile: { applicantTypeId: 'mockApplicantTypeId' },
        },
        answers: { keyA: 'value1' },
      } as unknown as ApplicationSubmissionEvent);
      expect(mockLogger.error).toBeCalledWith(
        { error: new Error('callback error') },
        'VerificationService.verify: unexpected error returned from client',
      );
    });

    it('should save the results on a successful callback', async () => {
      const mockSaveFn = vi.fn();
      const mockVerifyFn = vi.fn();
      const mockService = new VerificationService({
        addressService: {} as unknown as AddressService,
        configurationService: {
          findApplicationConfigs: vi.fn().mockResolvedValueOnce([
            {
              programId: 'mockProgramId',
              applicantTypeId: 'mockApplicantTypeId',
              configuration: {
                sections: [],
              },
            },
          ]),
        } as unknown as ConfigurationService,
        applicationVerificationRepository: {
          save: mockSaveFn,
        } as unknown as ApplicationVerificationRepository,
        eventProducer: {} as unknown as EventProducer,
        programDocumentService: {} as unknown as ProgramDocumentService,
        programFeatureService: {
          hasFeatures: vi.fn().mockResolvedValueOnce(true),
        } as unknown as ProgramFeatureService,
        verificationRepository: {
          getConfigurations: vi.fn().mockResolvedValueOnce({
            configurations: [
              {
                applicantTypeId: 'mockApplicantTypeId',
                programId: 'mockProgramId',
                service: ServiceType.DataLookup,
                dataLookup: { fields: [{ key: 'keyA' }] },
              },
            ],
          }),
          verify: mockVerifyFn.mockResolvedValueOnce({
            confidence: 0.98765,
            details: [
              { key: 'keyA', weight: 0.5, confidence: 1 },
              { key: 'keyB', weight: 0.5, confidence: 0.965 },
            ],
          }),
        } as unknown as VerificationRepository,
      });
      await mockService.verify({
        applicationVersionId: 'mockVersionId',
        programId: 'mockProgramId',
        submitter: {
          id: 'mockSubmitterId',
          applicantProfile: { applicantTypeId: 'mockApplicantTypeId' },
        },
        answers: { keyA: 'value1' },
      } as unknown as ApplicationSubmissionEvent);
      expect(mockSaveFn).toBeCalledWith({
        applicationVersionId: 'mockVersionId',
        confidence: 0.98765,
        details: [
          { confidence: 1, key: 'keyA', weight: 0.5 },
          { confidence: 0.965, key: 'keyB', weight: 0.5 },
        ],
        service: 'DataLookup',
      });
    });

    describe('address population', () => {
      const mockConfig = {
        sections: [
          {
            key: 'section1',
            name: 'Section 1',
            overview: { title: 'Section 1', description: 'Description' },
            questionGroups: [
              {
                key: 'group1',
                name: 'Group 1',
                questions: [
                  {
                    key: 'question1',
                    fields: [
                      {
                        key: 'homeAddress',
                        type: FieldType.Address,
                        displayName: 'Home Address',
                      },
                    ],
                  },
                  {
                    key: 'question2',
                    fields: [
                      {
                        key: 'workAddress',
                        type: FieldType.Address,
                        displayName: 'Work Address',
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      } as ApplicationConfig;
      it('should format address fields when addressKey is in config', async () => {
        const mockVerifyFn = vi.fn();
        const mockAddressService = {
          findById: vi.fn().mockResolvedValueOnce({
            addressLine1: '123 Main St',
            addressLine2: 'Apt 4B',
            city: 'New York',
            state: 'NY',
            zip: '10001',
          }),
        };
        const mockService = new VerificationService({
          addressService: mockAddressService as unknown as AddressService,
          configurationService: {
            findApplicationConfigs: vi.fn().mockResolvedValueOnce([
              {
                programId: 'mockProgramId',
                applicantTypeId: 'mockApplicantTypeId',
                configuration: mockConfig,
              },
            ]),
          } as unknown as ConfigurationService,
          applicationVerificationRepository: {} as unknown as ApplicationVerificationRepository,
          eventProducer: {} as unknown as EventProducer,
          programDocumentService: {} as unknown as ProgramDocumentService,
          programFeatureService: {
            hasFeatures: vi.fn().mockResolvedValueOnce(true),
          } as unknown as ProgramFeatureService,
          verificationRepository: {
            getConfigurations: vi.fn().mockResolvedValueOnce({
              configurations: [
                {
                  programId: 'mockProgramId',
                  applicantTypeId: 'mockApplicantTypeId',
                  service: ServiceType.DataLookup,
                  dataLookup: { fields: [{ key: 'homeAddress' }] },
                },
              ],
            }),
            verify: mockVerifyFn,
          } as unknown as VerificationRepository,
        });
        await mockService.verify({
          applicationVersionId: 'mockVersionId',
          programId: 'mockProgramId',
          submitter: {
            id: 'mockSubmitterId',
            applicantProfile: { applicantTypeId: 'mockApplicantTypeId' },
          },
          answers: { homeAddressId: 'address123' },
        } as unknown as ApplicationSubmissionEvent);
        expect(mockAddressService.findById).toBeCalledWith('address123');
        expect(mockVerifyFn).toBeCalledWith({
          payload: [{ key: 'homeAddress', value: '123 Main St Apt 4B New York NY 10001' }],
          applicantTypeId: 'mockApplicantTypeId',
          programId: 'mockProgramId',
          service: 'DataLookup',
        });
      });

      it('should handle missing address gracefully when addressKey is in config', async () => {
        const mockVerifyFn = vi.fn();
        const mockAddressService = {
          findById: vi.fn().mockResolvedValueOnce(null),
        };
        const mockService = new VerificationService({
          addressService: mockAddressService as unknown as AddressService,
          configurationService: {
            findApplicationConfigs: vi.fn().mockResolvedValueOnce([
              {
                programId: 'mockProgramId',
                applicantTypeId: 'mockApplicantTypeId',
                configuration: mockConfig,
              },
            ]),
          } as unknown as ConfigurationService,
          applicationVerificationRepository: {} as unknown as ApplicationVerificationRepository,
          eventProducer: {} as unknown as EventProducer,
          programDocumentService: {} as unknown as ProgramDocumentService,
          programFeatureService: {
            hasFeatures: vi.fn().mockResolvedValueOnce(true),
          } as unknown as ProgramFeatureService,
          verificationRepository: {
            getConfigurations: vi.fn().mockResolvedValueOnce({
              configurations: [
                {
                  programId: 'mockProgramId',
                  applicantTypeId: 'mockApplicantTypeId',
                  service: ServiceType.DataLookup,
                  dataLookup: { fields: [{ key: 'homeAddress' }] },
                },
              ],
            }),
            verify: mockVerifyFn,
          } as unknown as VerificationRepository,
        });
        await mockService.verify({
          applicationVersionId: 'mockVersionId',
          programId: 'mockProgramId',
          submitter: {
            id: 'mockSubmitterId',
            applicantProfile: { applicantTypeId: 'mockApplicantTypeId' },
          },
          answers: { homeAddressId: 'address123' },
        } as unknown as ApplicationSubmissionEvent);
        expect(mockAddressService.findById).toBeCalledWith('address123');
        expect(mockVerifyFn).toBeCalledWith({
          payload: [{ key: 'homeAddress', value: 'address123' }],
          applicantTypeId: 'mockApplicantTypeId',
          programId: 'mockProgramId',
          service: 'DataLookup',
        });
      });

      it('should handle multiple address fields in config', async () => {
        const mockVerifyFn = vi.fn();
        const mockAddressService = {
          findById: vi
            .fn()
            .mockResolvedValueOnce({
              addressLine1: '123 Main St',
              city: 'New York',
              state: 'NY',
              zip: '10001',
            })
            .mockResolvedValueOnce({
              addressLine1: '456 Oak Ave',
              addressLine2: 'Suite 200',
              city: 'Boston',
              state: 'MA',
              zip: '02101',
            }),
        };
        const mockService = new VerificationService({
          addressService: mockAddressService as unknown as AddressService,
          configurationService: {
            findApplicationConfigs: vi.fn().mockResolvedValueOnce([
              {
                programId: 'mockProgramId',
                applicantTypeId: 'mockApplicantTypeId',
                configuration: mockConfig,
              },
            ]),
          } as unknown as ConfigurationService,
          applicationVerificationRepository: {} as unknown as ApplicationVerificationRepository,
          eventProducer: {} as unknown as EventProducer,
          programDocumentService: {} as unknown as ProgramDocumentService,
          programFeatureService: {
            hasFeatures: vi.fn().mockResolvedValueOnce(true),
          } as unknown as ProgramFeatureService,
          verificationRepository: {
            getConfigurations: vi.fn().mockResolvedValueOnce({
              configurations: [
                {
                  programId: 'mockProgramId',
                  applicantTypeId: 'mockApplicantTypeId',
                  service: ServiceType.DataLookup,
                  dataLookup: { fields: [{ key: 'homeAddress' }, { key: 'workAddress' }] },
                },
              ],
            }),
            verify: mockVerifyFn,
          } as unknown as VerificationRepository,
        });
        await mockService.verify({
          applicationVersionId: 'mockVersionId',
          programId: 'mockProgramId',
          submitter: {
            id: 'mockSubmitterId',
            applicantProfile: { applicantTypeId: 'mockApplicantTypeId' },
          },
          answers: { homeAddressId: 'address123', workAddressId: 'address456' },
        } as unknown as ApplicationSubmissionEvent);
        expect(mockAddressService.findById).toBeCalledWith('address123');
        expect(mockAddressService.findById).toBeCalledWith('address456');
        expect(mockVerifyFn).toBeCalledWith({
          payload: [
            { key: 'homeAddress', value: '123 Main St New York NY 10001' },
            { key: 'workAddress', value: '456 Oak Ave Suite 200 Boston MA 02101' },
          ],
          applicantTypeId: 'mockApplicantTypeId',
          programId: 'mockProgramId',
          service: 'DataLookup',
        });
      });
    });
  });
  describe('uploadFile', () => {
    it('should return early if the program does not have verification features enabled', async () => {
      const mockUploadFileFn = vi.fn();
      const mockService = new VerificationService({
        addressService: {} as unknown as AddressService,
        configurationService: {} as unknown as ConfigurationService,
        applicationVerificationRepository: {} as unknown as ApplicationVerificationRepository,
        eventProducer: {} as unknown as EventProducer,
        programDocumentService: {} as unknown as ProgramDocumentService,
        programFeatureService: {
          hasFeatures: vi.fn().mockResolvedValueOnce(false),
        } as unknown as ProgramFeatureService,
        verificationRepository: {
          uploadFile: mockUploadFileFn,
        } as unknown as VerificationRepository,
      });
      const response = await mockService.uploadFile({
        applicantTypeId: 'mockApplicantTypeId',
        programDocumentId: 'mockDocumentId',
        programId: 'mockProgramId',
        filepath: 'mockValidationFile.csv',
        fields: [{ key: 'keyA' }],
      } as UploadVerificationFileEvent);
      expect(response).toBeUndefined();
      expect(mockUploadFileFn).not.toBeCalled();
    });

    it('should call the verification client if the features are enabled', async () => {
      const mockUploadFileFn = vi.fn();
      const mockService = new VerificationService({
        addressService: {} as unknown as AddressService,
        configurationService: {} as unknown as ConfigurationService,
        applicationVerificationRepository: {} as unknown as ApplicationVerificationRepository,
        eventProducer: {} as unknown as EventProducer,
        programDocumentService: { save: vi.fn() } as unknown as ProgramDocumentService,
        programFeatureService: {
          hasFeatures: vi.fn().mockResolvedValueOnce(true),
        } as unknown as ProgramFeatureService,
        verificationRepository: {
          uploadFile: mockUploadFileFn,
        } as unknown as VerificationRepository,
      });
      await mockService.uploadFile({
        applicantTypeId: 'mockApplicantTypeId',
        programDocumentId: 'mockDocumentId',
        programId: 'mockProgramId',
        filepath: 'mockValidationFile.csv',
        fields: [{ key: 'keyA' }],
      } as UploadVerificationFileEvent);
      expect(mockUploadFileFn).toBeCalledWith({
        applicantTypeId: 'mockApplicantTypeId',
        programId: 'mockProgramId',
        service: 'DataLookup',
        filepath: 'mockValidationFile.csv',
        fields: [{ key: 'keyA' }],
      });
    });

    it('should log errors and update program document with Failed status in the callback', async () => {
      const mockSaveFn = vi.fn();
      const mockUploadFileFn = vi.fn();
      const mockService = new VerificationService({
        addressService: {} as unknown as AddressService,
        configurationService: {} as unknown as ConfigurationService,
        applicationVerificationRepository: {} as unknown as ApplicationVerificationRepository,
        eventProducer: {} as unknown as EventProducer,
        programDocumentService: {
          save: mockSaveFn,
        } as unknown as ProgramDocumentService,
        programFeatureService: {
          hasFeatures: vi.fn().mockResolvedValueOnce(true),
        } as unknown as ProgramFeatureService,
        verificationRepository: {
          uploadFile: mockUploadFileFn.mockRejectedValueOnce(new Error('callback error')),
        } as unknown as VerificationRepository,
      });
      await mockService.uploadFile({
        applicantTypeId: 'mockApplicantTypeId',
        programDocumentId: 'mockDocumentId',
        programId: 'mockProgramId',
        filepath: 'mockValidationFile.csv',
        fields: [{ key: 'keyA' }],
      } as UploadVerificationFileEvent);
      expect(mockUploadFileFn).toBeCalledWith({
        applicantTypeId: 'mockApplicantTypeId',
        programId: 'mockProgramId',
        service: 'DataLookup',
        filepath: 'mockValidationFile.csv',
        fields: [{ key: 'keyA' }],
      });
      expect(mockLogger.error).toBeCalledWith(
        { error: new Error('callback error') },
        'VerificationService.uploadFile: unexpected error returned from client => program: mockProgramId, document: mockDocumentId',
      );
      expect(mockSaveFn).toBeCalledWith({
        id: 'mockDocumentId',
        status: 'Failed',
      });
    });

    it('should update program document with Completed status on a successful callback', async () => {
      const mockSaveFn = vi.fn();
      const mockUploadFileFn = vi.fn();
      const mockService = new VerificationService({
        addressService: {} as unknown as AddressService,
        configurationService: {} as unknown as ConfigurationService,
        applicationVerificationRepository: {} as unknown as ApplicationVerificationRepository,
        eventProducer: {} as unknown as EventProducer,
        programDocumentService: {
          save: mockSaveFn,
        } as unknown as ProgramDocumentService,
        programFeatureService: {
          hasFeatures: vi.fn().mockResolvedValueOnce(true),
        } as unknown as ProgramFeatureService,
        verificationRepository: {
          uploadFile: mockUploadFileFn.mockResolvedValueOnce({ message: 'success' }),
        } as unknown as VerificationRepository,
      });
      await mockService.uploadFile({
        applicantTypeId: 'mockApplicantTypeId',
        programDocumentId: 'mockDocumentId',
        programId: 'mockProgramId',
        filepath: 'mockValidationFile.csv',
        fields: [{ key: 'keyA' }],
      } as UploadVerificationFileEvent);
      expect(mockUploadFileFn).toBeCalledWith({
        applicantTypeId: 'mockApplicantTypeId',
        programId: 'mockProgramId',
        service: 'DataLookup',
        filepath: 'mockValidationFile.csv',
        fields: [{ key: 'keyA' }],
      });
      expect(mockSaveFn).toBeCalledWith({
        id: 'mockDocumentId',
        status: 'Completed',
      });
    });
  });
  describe('upsertLookupConfig', () => {
    it('should return early if the program does not have verification features enabled', async () => {
      const mockUpsertLookupFn = vi.fn();
      const mockService = new VerificationService({
        addressService: {} as unknown as AddressService,
        configurationService: {} as unknown as ConfigurationService,
        applicationVerificationRepository: {} as unknown as ApplicationVerificationRepository,
        eventProducer: {} as unknown as EventProducer,
        programDocumentService: {} as unknown as ProgramDocumentService,
        programFeatureService: {
          hasFeatures: vi.fn().mockResolvedValueOnce(false),
        } as unknown as ProgramFeatureService,
        verificationRepository: {
          upsertLookupConfig: mockUpsertLookupFn,
        } as unknown as VerificationRepository,
      });
      const response = await mockService.upsertLookupConfig({
        id: 'mockProgramId',
        applicantTypeId: 'mockApplicantTypeId',
        fields: [],
      });
      expect(response).toBeUndefined();
      expect(mockUpsertLookupFn).not.toBeCalled();
    });

    it('should call the verification client if the features are enabled', async () => {
      const mockUpsertLookupFn = vi.fn();
      const mockService = new VerificationService({
        addressService: {} as unknown as AddressService,
        configurationService: {} as unknown as ConfigurationService,
        applicationVerificationRepository: {} as unknown as ApplicationVerificationRepository,
        eventProducer: {} as unknown as EventProducer,
        programDocumentService: { save: vi.fn() } as unknown as ProgramDocumentService,
        programFeatureService: {
          hasFeatures: vi.fn().mockResolvedValueOnce(true),
        } as unknown as ProgramFeatureService,
        verificationRepository: {
          upsertLookupConfig: mockUpsertLookupFn,
        } as unknown as VerificationRepository,
      });
      await mockService.upsertLookupConfig({
        id: 'mockProgramId',
        applicantTypeId: 'mockApplicantTypeId',
        fields: [],
      });
      expect(mockUpsertLookupFn).toBeCalledWith({
        programId: 'mockProgramId',
        applicantTypeId: 'mockApplicantTypeId',
        fields: [],
      });
    });

    it('should log errors', async () => {
      const mockUpsertLookupFn = vi.fn();
      const mockService = new VerificationService({
        addressService: {} as unknown as AddressService,
        configurationService: {} as unknown as ConfigurationService,
        applicationVerificationRepository: {} as unknown as ApplicationVerificationRepository,
        eventProducer: {} as unknown as EventProducer,
        programDocumentService: {
          save: vi.fn(),
        } as unknown as ProgramDocumentService,
        programFeatureService: {
          hasFeatures: vi.fn().mockResolvedValueOnce(true),
        } as unknown as ProgramFeatureService,
        verificationRepository: {
          upsertLookupConfig: mockUpsertLookupFn.mockRejectedValueOnce(new Error('oops')),
        } as unknown as VerificationRepository,
      });
      await mockService.upsertLookupConfig({
        id: 'mockProgramId',
        applicantTypeId: 'mockApplicantTypeId',
        fields: [],
      });
      expect(mockUpsertLookupFn).toBeCalledWith({
        programId: 'mockProgramId',
        applicantTypeId: 'mockApplicantTypeId',
        fields: [],
      });
      expect(mockLogger.error).toBeCalledWith(
        { error: new Error('oops') },
        'VerificationService.updateLookupConfig: unexpected error returned from client => program: mockProgramId',
      );
    });

    it('should update program document with Completed status on a successful callback', async () => {
      const mockUpsertLookupFn = vi.fn();
      const mockService = new VerificationService({
        addressService: {} as unknown as AddressService,
        configurationService: {} as unknown as ConfigurationService,
        applicationVerificationRepository: {} as unknown as ApplicationVerificationRepository,
        eventProducer: {} as unknown as EventProducer,
        programDocumentService: {
          save: vi.fn(),
        } as unknown as ProgramDocumentService,
        programFeatureService: {
          hasFeatures: vi.fn().mockResolvedValueOnce(true),
        } as unknown as ProgramFeatureService,
        verificationRepository: {
          upsertLookupConfig: mockUpsertLookupFn.mockResolvedValueOnce({ message: 'success' }),
        } as unknown as VerificationRepository,
      });
      await mockService.upsertLookupConfig({
        id: 'mockProgramId',
        applicantTypeId: 'mockApplicantTypeId',
        fields: [],
      });
      expect(mockUpsertLookupFn).toBeCalledWith({
        programId: 'mockProgramId',
        applicantTypeId: 'mockApplicantTypeId',
        fields: [],
      });
    });
  });
});
