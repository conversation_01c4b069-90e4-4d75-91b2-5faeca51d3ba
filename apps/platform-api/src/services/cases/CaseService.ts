import {
  Case,
  CaseFilter,
  CaseMetadata,
  CaseSortColumn,
  CaseTag,
  CreateCaseInput,
  CreateCaseTagInput,
  DeleteCaseTagInput,
  InviteCaseParticipantInput,
  LinkAttemptResult,
  MutationResponse,
  OffsetPagination,
  Page,
  Program,
  RemoveEntityDocumentsInput,
  Sort,
  UnlinkCaseParticipantInput,
  UploadEntityDocumentsInput,
  WorkflowAction,
} from '@bybeam/platform-types';
import {
  CaseMetadataRepository,
  CaseRepository,
  LinkingRepository,
} from '@platform-api/@types/repositories/index.js';
import {
  CaseParticipantService,
  CaseTagService,
  DocumentService,
  CaseService as ICaseService,
  ProgramService,
  WorkflowEventService,
} from '@platform-api/@types/services.js';
import { In, Not } from 'typeorm';
import {
  AdminToken,
  AuthenticatedToken,
  LoginToken,
  isAdminToken,
} from '../../@types/authentication.js';
import dayjs from '../../utils/dayJsConfig.js';
import { logger } from '../../utils/logger.js';
import * as response from '../../utils/response.js';
import QueryServiceImplementation from '../QueryService.js';
import OneToOneDataLoader from '../utils/OneToOneDataLoader.js';

export default class CaseService
  extends QueryServiceImplementation<Case, CaseRepository>
  implements ICaseService
{
  private caseMetadataRepository: CaseMetadataRepository;
  private caseParticipantService: CaseParticipantService;
  private caseTagService: CaseTagService;
  private documentService: DocumentService;
  private programService: ProgramService;
  private linkingRepository: LinkingRepository;
  private workflowEventService: WorkflowEventService;

  public constructor(
    repository: CaseRepository,
    caseMetadataRepository: CaseMetadataRepository,
    caseParticipantService: CaseParticipantService,
    caseTagService: CaseTagService,
    documentService: DocumentService,
    programService: ProgramService,
    linkingRepository: LinkingRepository,
    workflowEventService: WorkflowEventService,
  ) {
    super(repository);
    this.caseMetadataRepository = caseMetadataRepository;
    this.caseParticipantService = caseParticipantService;
    this.caseTagService = caseTagService;
    this.documentService = documentService;
    this.programService = programService;
    this.linkingRepository = linkingRepository;
    this.workflowEventService = workflowEventService;
  }

  private readonly caseMetadataDataloader = new OneToOneDataLoader(
    (ids: string[]) => this.caseMetadataRepository.findBy({ caseId: In(ids) }),
    () => undefined,
    ({ caseId }) => caseId,
  );

  public async create({ programId, name, priority }: CreateCaseInput): Promise<Case> {
    return this.repository.save({
      programId,
      name,
      ...(priority && { priority }),
      statusUpdatedAt: dayjs.utc().toDate(),
    });
  }

  public async findAndCount(
    token: LoginToken,
    filter?: CaseFilter,
    pagination: OffsetPagination = { take: 50, page: 0 },
    sort?: Sort<CaseSortColumn>,
  ): Promise<Page<Case>> {
    // TODO: follow up with more fixes for PLT-2870
    if (!isAdminToken(token)) {
      return this.findAndCountInternal(
        {
          id: In(filter?.ids || []),
          applications: {
            submitterId: token.userId,
          },
        },
        pagination,
        ['applications'],
      );
    }

    let search = filter?.search?.trim();
    if (search && search?.length > 99) {
      logger.warn(
        { search },
        `${this.constructor.name}.findAndCount: long search string found, truncating`,
      );
      search = search.substring(0, 99);
    }

    let partnerPrograms: Program[] | undefined;
    if (!filter?.programId)
      partnerPrograms = await this.programService.findByPartnerId(token.partnerId);

    const { ids, count } = await this.repository.findAndCountIds(
      {
        ...filter,
        search,
        partnerId: token.partnerId,
        ...(partnerPrograms && { partnerProgramIds: partnerPrograms.map(({ id }) => id) }),
      },
      pagination,
      sort,
      token,
    );

    // Handle the case when a case no longer exists because its application(s)
    // were linked to another case via multiparty linking.
    if (filter?.ids?.length === 1 && ids.length === 0) {
      const participant = await this.caseParticipantService.findWithOptions({
        where: {
          linkAttempts: { originalCaseId: filter.ids[0], result: LinkAttemptResult.Success },
        },
        order: { createdAt: 'DESC' },
      });
      if (participant)
        return this.findAndCount(token, { ...filter, ids: [participant.caseId] }, pagination, sort);
    }

    const unsortedNodes = await this.findByIds(ids);
    const nodes = ids.map((id) => unsortedNodes.find(({ id: nodeId }) => id === nodeId)) as Case[];
    return { nodes, pageInfo: { count } };
  }

  public async findMetadataByCaseId(id: string): Promise<CaseMetadata | undefined> {
    return this.caseMetadataDataloader.load(id);
  }

  public async update(ids: string[], update: Partial<Case>): Promise<Case[]> {
    if (ids.length === 0) return [];
    await this.repository.update(ids, update);
    return this.findByIds(ids);
  }

  public async uploadDocuments(
    token: LoginToken,
    { id, files }: UploadEntityDocumentsInput,
  ): Promise<MutationResponse<Case>> {
    try {
      const { programId, documents: existingDocuments } = await this.repository.findOneOrFail({
        where: { id },
        relations: ['documents'],
      });
      const documents = await this.documentService.upload(token, {
        relation: { type: 'case', id },
        files,
        programId,
      });
      await this.repository.save({
        id,
        documents: [...existingDocuments, ...documents],
      });
      this.resetCache(id);
      logger.info(
        { documents: documents.map(({ id }) => id) },
        `${this.constructor.name}.uploadDocuments: documents uploaded successfully for case ${
          id || ''
        }`,
      );
      return response.success({ recordId: id, record: await this.findById(id) });
    } catch (error) {
      logger.error(
        { error },
        `${this.constructor.name}.uploadDocuments: unexpected error for case ${id} >`,
      );
      return response.error();
    }
  }

  public async removeDocuments({
    id,
    documentIds,
  }: RemoveEntityDocumentsInput): Promise<MutationResponse<Case>> {
    try {
      const { documents: existingDocuments } = await this.repository.findOneOrFail({
        where: { id },
        relations: ['documents'],
      });
      await this.repository.save({
        id,
        documents: existingDocuments.filter(({ id }) => !documentIds.includes(id)),
      });
      this.resetCache(id);
      logger.info(
        { documents: documentIds },
        `${this.constructor.name}.removeDocuments: documents removed successfully for case ${
          id || ''
        }`,
      );
      return response.success({ recordId: id, record: await this.findById(id) });
    } catch (error) {
      logger.error(
        { error },
        `${this.constructor.name}.removeDocuments: unexpected error for case ${id} >`,
      );
      return response.error();
    }
  }

  public async inviteParticipant(
    token: AdminToken,
    { id, participant }: InviteCaseParticipantInput,
  ): Promise<MutationResponse<Case>> {
    try {
      await this.linkingRepository.inviteCaseParticipant({
        caseId: id,
        participant: {
          ...participant,
          autoLink: participant.autoLink ?? false,
        },
      });
      await this.workflowEventService.create(token, {
        entityId: id,
        entityType: 'case',
        action: WorkflowAction.InviteCaseParticipant,
        newValue: JSON.stringify(participant),
      });
      return response.success({ recordId: id, record: await this.findById(id) });
    } catch (error) {
      logger.error(
        { error },
        `${this.constructor.name}.inviteParticipant: unexpected error for case ${id} >`,
      );
      return response.error();
    }
  }

  public async unlinkParticipant(
    token: AdminToken,
    { id, caseParticipantId }: UnlinkCaseParticipantInput,
  ): Promise<MutationResponse<Case>> {
    try {
      await this.linkingRepository.unlinkCaseParticipant({ caseParticipantId });
      await this.workflowEventService.create(token, {
        entityId: id,
        entityType: 'case',
        action: WorkflowAction.UnlinkCaseParticipant,
        previousValue: JSON.stringify({ caseParticipantId }),
      });
      return response.success({ recordId: id, record: await this.findById(id) });
    } catch (error) {
      logger.error(
        { error },
        `${this.constructor.name}.unlinkParticipant: unexpected error for case ${id} >`,
      );
      return response.error();
    }
  }

  public async addTagToCase(
    AdminToken: AdminToken,
    input: CreateCaseTagInput,
  ): Promise<MutationResponse<CaseTag>> {
    return await this.caseTagService.create({ ...input, userId: AdminToken.userId });
  }

  public async removeTagFromCase(
    AdminToken: AdminToken,
    input: DeleteCaseTagInput,
  ): Promise<MutationResponse<CaseTag>> {
    return await this.caseTagService.delete({ ...input, userId: AdminToken.userId });
  }

  public async findWorkflowEvents(
    token: AuthenticatedToken,
    caseId: string,
    applicationId: string,
  ) {
    const isAdmin = isAdminToken(token);

    const whereClause = await (async () => {
      if (isAdmin) {
        const fulfillmentIds =
          (
            await this.repository.findOne({
              where: { id: caseId },
              relations: ['fulfillments'],
              withDeleted: true,
            })
          )?.fulfillments?.map((fulfillment) => fulfillment.id) ?? [];
        return {
          entityId: In([caseId, applicationId, ...fulfillmentIds]),
          action: Not(WorkflowAction.PaymentUpdate),
        };
      }
      return {
        entityId: In([caseId, applicationId]),
        action: In([
          WorkflowAction.CaseComment,
          WorkflowAction.ChangeStatus,
          WorkflowAction.Submission,
        ]),
      };
    })();

    return this.workflowEventService.findManyWithOptions({
      where: whereClause,
      order: { createdAt: 'ASC' },
    });
  }
}
