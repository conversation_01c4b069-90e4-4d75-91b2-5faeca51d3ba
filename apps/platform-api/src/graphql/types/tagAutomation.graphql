type TagAutomation {
  id: UUID!
  triggerType: TagAutomationTriggerType!
  actionType: TagAutomationActionType!
  criteria: JSON
  programId: UUID
  program: Program
  partnerId: UUID!
  partner: Partner!
  createdAt: DateTime!
  updatedAt: DateTime!
  deactivatedAt: DateTime
  tags: [Tag!]!
}

enum TagAutomationTriggerType {
  APPLICATION_SUBMITTED
}

enum TagAutomationActionType {
  ADD_TAG
}

input CreateTagAutomationInput {
  triggerType: TagAutomationTriggerType!
  actionType: TagAutomationActionType!
  criteria: JSON
  programId: UUID
  tagIds: [UUID!]!
}

input UpdateTagAutomationInput {
  id: UUID!
  triggerType: TagAutomationTriggerType
  actionType: TagAutomationActionType
  criteria: JSON
  programId: UUID
  tagIds: [UUID!]
  deactivate: Boolean
}

input DeleteTagAutomationInput {
  id: UUID!
}

type TagAutomationMutationResponse {
  metadata: ResponseMetadata!
  record: TagAutomation
  query: Query!
}

type TagAutomationsMutations {
  create(input: CreateTagAutomationInput!): TagAutomationMutationResponse!
  update(input: UpdateTagAutomationInput!): TagAutomationMutationResponse!
  delete(input: DeleteTagAutomationInput!): TagAutomationMutationResponse!
}

type Query {
  tagAutomations: [TagAutomation!]!
}

type Mutation {
  tagAutomations: TagAutomationsMutations
}
