enum CaseStatus {
  InProgress
  Incomplete
  ReadyForReview
  InReview
  PendingCertification
  FiscalReview
  PaymentSent
  Denied
  Withdrawn
  Archived
  Approved
}

type CaseMetadata {
  denialReason: String
  withdrawalReason: String
  incompleteReason: String
  intakeStaff: String
}

type Case {
  id: UUID!
  displayId: String!
  program: Program!
  status: CaseStatus!
  metadata: CaseMetadata!
  name: NonEmptyString!
  createdAt: DateTime!
  deactivatedAt: DateTime
  statusUpdatedAt: DateTime
  decisionReachedAt: DateTime
  priority: NonNegativeInt!
  assignee: Admin
  notes: [Note!]!
  applications: [Application!]!
  documents: [Document!]!
  fulfillments: [Fulfillment!]!
  participants: [CaseParticipant!]!
  caseTags: [CaseTag!]!
  workflowEvents: [WorkflowEvent!]!
  comments: [Comment!]
}

enum CaseSortColumn {
  CreatedAt
  UpdatedAt
  SubmittedAt
  PropertyAddress
  ApplicantName
  AMIBracket
  Assignee
  Status
  AwardedAmount
  ProgramName
  Eligibility
}

# Canonical eligibility statuses shared across API and UI
input StatusFilters {
  archived: Boolean
  expedited: Boolean
  newApplicant: Boolean
  referred: Boolean
  applicantTypeId: [UUID!]
  caseParticipantStatus: [CaseParticipantStatus!]
  verified: [VerificationStatus!]
  tags: [UUID!]
  documentTagTypes: [String!]
  answerReviews: [String!]
  # Exact match on latest application version eligibility label
  eligibility: [EligibilityStatus!]
  versionCount: [String!]
  paymentStatus: [PaymentStatus!]
}

input CaseFilter {
  ids: [UUID!]
  programId: UUID
  search: String
  searchCategories: [String!]
  status: [CaseStatus!]
  assigneeId: [AssigneeId!]
  statusFilters: StatusFilters
  searchAnswersKey: String
  searchAnswersValue: String
}

input CaseSort {
  column: CaseSortColumn!
  direction: SortDirection!
}

type CasePage {
  cases: [Case!]!
  pageInfo: PageInfo!
}

input TransitionStatusAssignment {
  assigneeId: UUID
}

input TransitionCaseStatusesInput {
  ids: [UUID!]!
  status: CaseStatus
  reason: String @constraint(minLength: 1, maxLength: 600)
  # When assignment is not provided, that indicates autoassign should be attempted
  # If assignment is provided but assigneeId is null, that indicates unassignment
  assignment: TransitionStatusAssignment
}

type CaseMutationResponse {
  metadata: ResponseMetadata!
  query: Query!
  record: Case
}

input AssignToCaseInput {
  ids: [UUID!]!
  assigneeId: UUID
}

input ExpediteCasesInput {
  ids: [UUID!]!
  reason: String! @constraint(minLength: 1, maxLength: 600)
}

input ApproveCasePaymentsInput {
  ids: [UUID!]!
  fulfillmentIds: [UUID!]
}

input OverridePaymentInput {
  fundId: UUID!

  amount: NonNegativeInt!
  payeeType: PayeeType!
  applicantTypeId: UUID
  # TODO: When implementing Partner-Issued bulk payments, more fields will need
  # to be added here. Leaving them out for now for the sake of simplicity.
}

input OverrideCasePaymentsInput {
  ids: [UUID!]!
  payment: OverridePaymentInput!
}

type CasesMutationResponse {
  metadata: ResponseMetadata!
  query: Query!
  record: [Case!]
}

type BulkCaseMutationResponse {
  metadata: BulkResponseMetadata!
  query: Query!
  records: [Case!]
}

input ParticipantInput {
  applicantTypeId: UUID!
  name: String!
  email: String!
  autoLink: Boolean
}

input InviteCaseParticipantInput {
  id: UUID!
  participant: ParticipantInput
}

input UnlinkCaseParticipantInput {
  id: UUID!
  caseParticipantId: UUID!
}

input AddCommentInput {
  caseId: UUID!
  content: NonEmptyString!
  parentCommentId: UUID
}

input AddBulkCommentInput {
  ids: [UUID!]!
  content: NonEmptyString!
}

type CaseMutations {
  approvePayments(input: ApproveCasePaymentsInput!): BulkCaseMutationResponse!
  overridePayments(input: OverrideCasePaymentsInput!): BulkCaseMutationResponse!
  transitionStatuses(
    input: TransitionCaseStatusesInput!
  ): BulkCaseMutationResponse!

  expedite(input: ExpediteCasesInput!): BulkCaseMutationResponse!
  undoExpedite(input: ExpediteCasesInput!): BulkCaseMutationResponse!

  removeDocuments(input: RemoveDocumentsInput!): CaseMutationResponse!
  uploadDocuments(input: UploadDocumentsInput!): CaseMutationResponse!

  inviteParticipant(input: InviteCaseParticipantInput!): CaseMutationResponse!
  unlinkParticipant(input: UnlinkCaseParticipantInput!): CaseMutationResponse!

  addTagToCase(input: CreateCaseTagInput!): CaseTagMutationResponse!
  removeTagFromCase(input: DeleteCaseTagInput!): CaseTagMutationResponse!

  addComment(input: AddCommentInput!): CaseMutationResponse!
  addBulkComment(input: AddBulkCommentInput!): BulkCaseMutationResponse!
  addParticipantComment(input: AddCommentInput!): CaseMutationResponse!
}
