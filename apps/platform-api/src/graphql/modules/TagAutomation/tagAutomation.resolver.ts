import { composeResolvers } from '@graphql-tools/resolvers-composition';
import IsAdmin from '../../../resolvers/composition/IsAdmin.js';
import type {
  GqlResolvers as Resolvers,
  GqlTagAutomationActionType,
  GqlTagAutomationTriggerType,
} from '../../../graphql/__generated__/resolvers-types.js';
import type {
  CreateTagAutomationInput,
  DeleteTagAutomationInput,
  TagAutomation,
  UpdateTagAutomationInput,
} from '@bybeam/platform-types';
import { AdminToken } from '@platform-api/@types/authentication.js';
import { logger } from '../../../utils/logger.js';

const tagAutomationResolvers: Resolvers = {
  Query: {
    tagAutomations: async (_root, _, context) => {
      return context.services.tagAutomations.findByPartnerId(context.token.partnerId);
    },
  },
  TagAutomation: {
    actionType: (tagAutomation: TagAutomation) => {
      // Convert platform-types enum to GraphQL enum
      return tagAutomation.actionType as unknown as GqlTagAutomationActionType;
    },
    triggerType: (tagAutomation: TagAutomation) => {
      // Convert platform-types enum to GraphQL enum
      return tagAutomation.triggerType as unknown as GqlTagAutomationTriggerType;
    },
  },
  Mutation: {
    tagAutomations: () => ({ _type: 'TagAutomationsMutations' }),
  },

  TagAutomationsMutations: {
    create: async (_root, { input }, context) => {
      logger.info('tagAutomationResolvers.TagAutomationsMutations.create');
      const token = context.token as AdminToken;
      return context.services.tagAutomations.create(
        token,
        input as unknown as CreateTagAutomationInput,
      );
    },
    update: async (_root, { input }, context) => {
      const token = context.token as AdminToken;
      return context.services.tagAutomations.update(
        token,
        input as unknown as UpdateTagAutomationInput,
      );
    },
    delete: async (_root, { input }, context) => {
      const token = context.token as AdminToken;
      return context.services.tagAutomations.delete(
        token,
        input as unknown as DeleteTagAutomationInput,
      );
    },
  },
};

const resolverComposition = {
  'Query.tagAutomations': [IsAdmin()],
  'TagAutomationsMutations.create': [IsAdmin()],
  'TagAutomationsMutations.update': [IsAdmin()],
  'TagAutomationsMutations.delete': [IsAdmin()],
};

export default composeResolvers(tagAutomationResolvers, resolverComposition);
