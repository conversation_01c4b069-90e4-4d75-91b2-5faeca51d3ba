schema:
  ## Include all GraphQL modules
  - src/graphql/modules/**/*.graphql
  ## Legacy graphql SDL conventions
  - src/graphql/types/**/*.graphql
  ## Generated scalars SDL (emitted by emit-scalars-sdl.cjs)
  - src/graphql/types/__generated__/**.graphql

# These hooks ensure we generate the correct SDL for scalars
# and constraints prior to building the actual types.
hooks:
  onWatchTriggered:
    - node ./scripts/emit-scalars-sdl.cjs
    - node ./scripts/emit-constraint-directive-sdl.mjs

generates:
  src/graphql/__generated__/resolvers-types.ts:
    plugins:
      - typescript
      - typescript-resolvers
    config:
      # Allow types to accept partial shapes from parent resolvers, delegating remaining logic to subfield resolvers.
      defaultMapper: "Partial<{T}>"
      useTypeImports: true
      # Prefix all generated GraphQL types to avoid name collisions with
      # domain models we import via `mappers` (e.g., `Case`).
      # Example: GraphQL type `Case` becomes `GqlCase` in this file.
      typesPrefix: Gql
      # Use a relative import with ESM extension so local builds don't depend on tsconfig paths
      # Resolves to apps/platform-api/src/@types/graphql.ts (emitted as graphql.js)
      # TODO: We'll need to incorporate other context types as we migrate to more modules.
      contextType: "../../@types/graphql.js#AuthenticatedContext"
      defaultScalarType: unknown
      # Map GraphQL types to our domain models so resolver return types
      # can return service-layer entities without reshaping.
      mappers:
        Case: "@bybeam/platform-types#Case"
        TagAutomation: "@bybeam/platform-types#TagAutomation"
      # Scalar mappings used throughout our schema
      scalars:
        DateTime: string
        UUID: string
        URL: string
        EmailAddress: string
        NonEmptyString: string
        NonNegativeInt: number
        JSON: unknown
        BigInt: bigint
        Upload: unknown
        # Additional custom scalars to avoid implicit any in generated types
        Date: string
        JWT: string
        Latitude: number
        Longitude: number
        PhoneNumber: string
        PositiveInt: number
        RoutingNumber: string
        AccountNumber: string
        AssigneeId: string
