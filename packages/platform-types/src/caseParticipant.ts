import { ApplicantType } from './applicantType.js';
import Application from './application.js';
import Case from './case.js';

interface ParticipantInput {
  applicantTypeId: string;
  name: string;
  email: string;
  autoLink?: boolean;
}

interface InviteCaseParticipantInput {
  id: string;
  participant: ParticipantInput;
}

interface UnlinkCaseParticipantInput {
  id: string;
  caseParticipantId: string;
}

interface CaseParticipant {
  id: string;
  caseId: string;
  case: Case;
  applicantTypeId: string;
  applicantType: ApplicantType;
  name: string;
  email: string;
  createdAt: Date;
  deactivatedAt?: Date;
  invitationCodes?: InvitationCode[];
  linkAttempts?: LinkAttempt[];
}

interface InvitationCode {
  id: string;
  caseParticipantId: string;
  caseParticipant: CaseParticipant;
  code: string;
  createdAt: Date;
  usedAt?: Date;
  deactivatedAt?: Date;
}

enum LinkAttemptResult {
  Success = 'success',
  Fail = 'fail',
}

interface LinkAttempt {
  id: string;
  applicationId: string;
  application: Application;
  originalCaseId: string;
  originalCase: Case;
  caseParticipantId?: string;
  caseParticipant?: CaseParticipant;
  result: LinkAttemptResult;
  details: string; // TODO type this
  createdAt: Date;
  deactivatedAt?: Date;
}

enum CaseParticipantStatus {
  Unlinked = 'Unlinked',
  PendingLink = 'PendingLink',
  FailedLink = 'FailedLink',
  Linked = 'Linked',
}

export interface CaseParticipantLinkStatusDetails {
  caseId: string;
  hasPendingLink: boolean;
  hasMissingParticipant: boolean;
  hasFailedLink: boolean;
}

export { CaseParticipantStatus, LinkAttemptResult };
export type {
  CaseParticipant,
  InvitationCode,
  LinkAttempt,
  InviteCaseParticipantInput,
  UnlinkCaseParticipantInput,
  ParticipantInput,
};
