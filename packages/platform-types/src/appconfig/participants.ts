interface Participant {
  // the value of name and email should be the application field key that
  // provides the participant data. repeated/complex fields are supported.
  // leave out the index: i.e. "landlord.name" will work for a single
  // field with that exact key or a complex field landlord with a
  // subkey of name, returning answers like "landlord.0.name"
  name: string;
  email: string;
  applicantTypeId: string; // applicant type ID for the intended participant
  autoLink?: boolean;
}

export type { Participant };
