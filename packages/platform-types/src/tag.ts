import { CaseTag } from './caseTag.js';
import Partner from './partner.js';
import Program from './program.js';

export enum TagAutomationTriggerType {
  ApplicationSubmitted = 'APPLICATION_SUBMITTED',
}

export enum TagAutomationActionType {
  AddTag = 'ADD_TAG',
}
export interface Tag {
  id: string;
  partnerId: string;
  partner: Partner;
  name: string;
  createdAt: Date;
  updatedAt: Date;
  deactivatedAt?: Date;
  count?: number;
  caseTags?: CaseTag[];
  tagAutomations?: TagAutomation[];
}

export interface CreateTagsInput {
  tags: Pick<Tag, 'name'>[];
}

export interface UpdateTagInput {
  id: string;
  name: string;
}

export interface DeleteTagsInput {
  ids: string[];
}

export enum CriteriaType {
  ApplicationScore = 'APPLICATION_SCORE',
}

export enum CriteriaOperator {
  eq = 'eq',
  in = 'in',
}

export type Criteria = {
  op: CriteriaOperator;
  value: unknown;
};
export type TagAutomationCriteria = Record<Partial<CriteriaType>, Criteria>;

export interface CreateTagAutomationInput {
  triggerType: TagAutomationTriggerType;
  actionType: TagAutomationActionType;
  criteria?: TagAutomationCriteria;
  programId?: string | null;
  tagIds: string[];
}

export interface UpdateTagAutomationInput {
  id: string;
  triggerType?: TagAutomationTriggerType;
  actionType?: TagAutomationActionType;
  programId?: string | null;
  tagIds?: string[];
  criteria?: TagAutomationCriteria;
  deactivate?: boolean;
}
export interface DeleteTagAutomationInput {
  id: string;
}

export interface TagAutomation {
  id: string;
  triggerType: TagAutomationTriggerType;
  criteria?: TagAutomationCriteria;
  actionType: TagAutomationActionType;
  programId?: string | null;
  program?: Program;
  partnerId: string;
  partner: Partner;
  createdAt: Date;
  updatedAt: Date;
  deactivatedAt?: Date;
  tags: Tag[];
}
