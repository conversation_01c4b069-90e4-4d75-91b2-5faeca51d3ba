import { Role } from '@bybeam/identity-client/types';
import Address from './address.js';
import { AnalyticsResource } from './analytics.js';
import { ApplicantType } from './applicantType.js';
import { EligibilityConfig } from './eligibility.js';
import { Fund } from './fund.js';
import {
  ApplicantProfileConfiguration,
  CommunicationChannelsConfiguration,
  PartnerConfig,
} from './partnerConfig.js';
import PartnerFeature from './partnerFeature.js';
import Program from './program.js';
import SavedView from './savedView.js';
import { Tag, TagAutomation } from './tag.js';

export interface PartnerWhitelabeling {
  id: string;
  partnerId: string;
  partner?: Partner;
  logo: string;
  platformName: string;
  brandColor: string;
  favicon: string;
  senderEmail?: string;
}

export interface Partner {
  id: string;
  // human-readable external identifier; used for things like the S3 bucket, URL prefix, etc
  externalId: string;
  name: string;
  features?: PartnerFeature[];
  funds?: Fund[];
  programs?: Program[];
  email: string;
  phone?: string;
  gleanInviteToken?: string;
  mailingAddressId?: string;
  mailingAddress?: Address;
  whitelabeling: PartnerWhitelabeling;
  parentId?: string;
  parent?: Partner;
  children?: Partner[];
  eligibility?: EligibilityConfig;
  analyticsResources?: AnalyticsResource[];
  config?: PartnerConfig;
  communicationChannels?: CommunicationChannelsConfiguration;
  applicantProfileConfigs?: ApplicantProfileConfiguration[];
  applicantTypes?: ApplicantType[];
  tags: Tag[];
  tagAutomations: TagAutomation[];
  roles?: Role[];
  savedViews?: SavedView[];
}
export default Partner;
