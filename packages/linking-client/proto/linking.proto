syntax = "proto3";

package linking;

import "common.proto";

import "google/api/annotations.proto";
import "google/protobuf/struct.proto";

service LinkingServer {
  rpc inviteCaseParticipant (InviteCaseParticipantRequest) returns (InviteCaseParticipantResponse);
  rpc unlinkCaseParticipant (UnlinkCaseParticipantRequest) returns (UnlinkCaseParticipantResponse);
  
  // handlers (should not be called directly)
  rpc handleApplicationSubmitted (common.ApplicationSubmittedEvent) returns (common.EventHandlerResponse) {
    option (google.api.http) = {
      post: "/applicationSubmitted"
      body: "*"
    };
  }
  
  rpc handleApplicationVersionCreated (common.ApplicationVersionCreatedEvent) returns (common.EventHandlerResponse) {
    option (google.api.http) = {
      post: "/applicationVersionCreated"
      body: "*"
    };
  }
}

message NewCaseParticipant {
  string name = 1;
  string email = 2;
  string applicant_type_id = 3;
  bool auto_link = 4;
}

enum LinkNotificationRecipient {
  unknownRecipient = 0;
  existingParticipant = 1;
  invitedParticipant = 2;
}

message InviteCaseParticipantRequest {
  string case_id = 1;
  // TODO repeated? (we can leave it as singular for now and change it to repeated later;
  // protobuf will interpret requests the same way)
  NewCaseParticipant participant = 2; 
}

message InviteCaseParticipantResponse { }

message UnlinkCaseParticipantRequest {
  string case_participant_id = 1;
}

message UnlinkCaseParticipantResponse {
  string new_case_id = 1;
}
