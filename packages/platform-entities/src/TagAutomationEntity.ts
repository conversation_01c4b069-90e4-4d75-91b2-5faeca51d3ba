import {
  Tag,
  TagAutomation,
  TagAutomationActionType,
  TagAutomationCriteria,
  TagAutomationTriggerType,
} from '@bybeam/platform-types';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import TagEntity from './TagEntity.js';
import PartnerEntity from './PartnerEntity.js';
import ProgramEntity from './ProgramEntity.js';

@Entity('tag_automations')
export class TagAutomationEntity implements TagAutomation {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({ type: 'text', name: 'trigger_type' })
  public triggerType: TagAutomationTriggerType;

  @Column({ type: 'text', name: 'action_type' })
  public actionType: TagAutomationActionType;

  @Column({ type: 'uuid', name: 'program_id', nullable: true })
  public programId?: string;

  @ManyToOne(() => ProgramEntity)
  @JoinColumn({ name: 'program_id' })
  public program?: ProgramEntity;

  @Column({ type: 'uuid', name: 'partner_id' })
  public partnerId: string;

  @ManyToOne(() => PartnerEntity, { eager: true })
  @JoinColumn({ name: 'partner_id' })
  public partner: PartnerEntity;

  @Column({ type: 'jsonb', nullable: true })
  public criteria?: TagAutomationCriteria;

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deactivated_at' })
  public deactivatedAt?: Date;

  @ManyToMany(() => TagEntity, { cascade: true, eager: true })
  @JoinTable({
    name: 'tags_tag_automations',
    joinColumn: { name: 'tag_automation_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'tag_id', referencedColumnName: 'id' },
  })
  public tags: Tag[];
}
