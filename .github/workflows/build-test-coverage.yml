name: Build, Test, and Coverage
env:
  TURBO_SCM_BASE: "origin/main"
on:
  pull_request:
    # paths-ignore:
    # - ".github/**"
    # - "cloudrun/**"
    # - "dbs/**"
    # - "templates/**"
    # - "turbo/**"
  merge_group:
jobs:
  coverage:
    name: report
    runs-on: github-xlarge
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Cache turbo build setup
        uses: actions/cache@v4
        with:
          path: .turbo
          key: ${{ runner.os }}-turbo-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-turbo-
      - name: Setup PNPM
        uses: pnpm/action-setup@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20.10.0
          cache: "pnpm"
      - name: Setup UV
        uses: astral-sh/setup-uv@v6
        with:
          version: 0.5.15
          activate-environment: true
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"
      - name: Install modules
        run: pnpm install
      - name: Validate GraphQL codegen artifacts
        run: pnpm --filter=@bybeam/platform-api codegen:ci-check
      - name: Build apps and packages
        run: pnpm build:ci
      - name: Setup Playwright with cache
        run: pnpm exec playwright install chromium --with-deps
      - name: Run partner portal tests
        run: pnpm test:partner-portal:ci
      - name: Run affected tests
        run: pnpm test:coverage:ci
      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          fail_ci_if_error: true

  go-checks:
    name: Go Lint & Test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.22"
          cache: true

      - uses: dominikh/staticcheck-action@v1
        with:
          version: "latest"
          working-directory: "apps/themis"

      - name: Run Gosec Security Scanner
        uses: securego/gosec@master

      - name: Upload Go test results (if any)
        if: success()
        uses: actions/upload-artifact@v4
        with:
          name: go-test-results
          path: |
            **/coverage.out
