name: "Setup Playwright with cache"
description: "Cache Playwright browsers and optionally install on cache miss"
inputs:
  browsers:
    description: "Space-separated list of browsers to install (e.g., 'chromium webkit firefox')"
    required: false
    default: "chromium"
  browsers-path:
    description: "Directory for Playwright browser binaries"
    required: false
    default: "~/.cache/ms-playwright"
  cache-key:
    description: "Cache key suffix; if omitted, uses hash of pnpm-lock.yaml"
    required: false
    default: ""
  with-deps:
    description: "Install system dependencies as well (true/false)"
    required: false
    default: "true"
runs:
  using: "composite"
  steps:
    - name: Cache Playwright browsers
      id: playwright-cache
      uses: actions/cache@v4
      with:
        path: ${{ inputs.browsers-path }}
        key: ${{ runner.os }}-playwright-${{ inputs.cache-key != '' && inputs.cache-key || hashFiles('pnpm-lock.yaml') }}
        restore-keys: |
          ${{ runner.os }}-playwright-

    - name: Install Playwright system dependencies
      if: steps.playwright-cache.outputs.cache-hit != 'true' && inputs.with-deps == 'true'
      shell: bash
      env:
        PLAYWRIGHT_BROWSERS_PATH: ${{ inputs.browsers-path }}
      run: |
        pnpm exec playwright install-deps ${{ inputs.browsers }}

    - name: Install Playwright browsers
      # Always run to ensure browsers match version; cache keeps this fast
      if: ${{ always() }}
      shell: bash
      env:
        PLAYWRIGHT_BROWSERS_PATH: ${{ inputs.browsers-path }}
      run: |
        pnpm exec playwright install ${{ inputs.browsers }}

